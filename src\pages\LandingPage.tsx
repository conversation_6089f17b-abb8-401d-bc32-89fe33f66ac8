import { Link } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';
import logoImage from '../assets/images/logo.png';
import { useEffect } from 'react';

const LandingPage = () => {
  // Clear localStorage when landing page is loaded
  useEffect(() => {
    // Clean up any existing storage data
    localStorage.removeItem('testAppUser');
    localStorage.removeItem('testAppToken');
    localStorage.removeItem('tabChangeCount');
    console.log('Cleared localStorage on landing page load');
  }, []);

  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Header */}
      <header className="w-full bg-white shadow-sm py-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center">
          <div className="flex items-center">
            <img src={logoImage} alt="Kambaa's Test App Logo" className="h-10 w-auto mr-2" />
            <span className="text-xl font-bold text-blue-600">Kambaa's Test App</span>
          </div>
          <div>
            <Link
              to="/student-login"
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors"
            >
              Student Login
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="flex-grow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 flex flex-col md:flex-row items-center">
          {/* Left Column - Text */}
          <div className="w-full md:w-1/2 md:pr-8 mb-10 md:mb-0">
            <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-4">
              <span className="block">Campus Interview</span>
              <span className="block text-blue-600">Assessment Platform</span>
            </h1>
            <p className="text-lg text-gray-600 mb-8">
              A comprehensive testing platform designed for students to assess logical reasoning and common sense through standardized tests.
            </p>
            <div className="flex justify-center md:justify-start">
              <Link
                to="/student-login"
                className="group relative inline-flex items-center justify-center w-64 px-8 py-4 text-base md:text-lg font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md shadow-lg transition-all duration-300 ease-in-out transform hover:translate-x-1 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Take a Test
                <ArrowRight className="ml-2 h-5 w-5 inline-block group-hover:animate-pulse" />
                <span className="absolute right-0 top-0 -mt-1 -mr-1 h-3 w-3 rounded-full bg-blue-400 animate-ping"></span>
              </Link>
            </div>
          </div>
          {/* Right Column - Image */}
          <div className="w-full md:w-1/2">
            <img 
              src="https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1740&q=80" 
              alt="Person using laptop" 
              className="w-full h-auto rounded-lg shadow-xl"
            />
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="bg-gray-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-sm font-semibold tracking-wide text-blue-600 uppercase">FEATURES</h2>
            <h3 className="mt-2 text-3xl font-bold text-gray-900">A better way to test candidates</h3>
            <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
              Our platform offers comprehensive testing solutions for educational institutions.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Feature 1 */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="bg-blue-100 rounded-full w-12 h-12 flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-2">Logical Reasoning Assessment</h4>
              <p className="text-gray-600">
                Test analytical thinking, pattern recognition, and problem-solving abilities with our logical reasoning questions.
              </p>
            </div>

            {/* Feature 2 */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="bg-blue-100 rounded-full w-12 h-12 flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"></path>
                </svg>
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-2">Common Sense Scenarios</h4>
              <p className="text-gray-600">
                Evaluate practical judgment and decision-making skills through real-world scenario questions.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-gray-500">© 2025 Kambaa's Test App. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage; 