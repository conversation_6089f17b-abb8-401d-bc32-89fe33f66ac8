import * as faceapi from 'face-api.js';

export interface FaceDetectionResult {
  faceCount: number;
  hasFace: boolean;
  hasMultipleFaces: boolean;
  confidence?: number;
  boundingBox?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

export interface FaceDetectionOptions {
  minConfidence?: number;
  detectionInterval?: number;
  modelPath?: string;
}

export class FaceDetectionService {
  private isInitialized = false;
  private isLoading = false;
  private detectionInterval: number | null = null;
  private options: Required<FaceDetectionOptions>;

  constructor(options: FaceDetectionOptions = {}) {
    this.options = {
      minConfidence: options.minConfidence || 0.5,
      detectionInterval: options.detectionInterval || 2000, // 2 seconds
      modelPath: options.modelPath || '/models', // Default path for face-api.js models
    };
  }

  /**
   * Initialize face-api.js models
   */
  async initialize(): Promise<void> {
    if (this.isInitialized || this.isLoading) {
      return;
    }

    this.isLoading = true;
    
    try {
      console.log('Loading face detection models...');
      
      // Load the required models for face detection
      await Promise.all([
        faceapi.nets.tinyFaceDetector.loadFromUri(this.options.modelPath),
        faceapi.nets.faceLandmark68Net.loadFromUri(this.options.modelPath),
        faceapi.nets.faceRecognitionNet.loadFromUri(this.options.modelPath),
      ]);

      this.isInitialized = true;
      console.log('Face detection models loaded successfully');
    } catch (error) {
      console.error('Failed to load face detection models:', error);
      throw new Error('Failed to initialize face detection. Please check if model files are available.');
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Detect faces in a video element
   */
  async detectFaces(videoElement: HTMLVideoElement): Promise<FaceDetectionResult> {
    if (!this.isInitialized) {
      throw new Error('Face detection service not initialized. Call initialize() first.');
    }

    if (!videoElement || videoElement.videoWidth === 0 || videoElement.videoHeight === 0) {
      return {
        faceCount: 0,
        hasFace: false,
        hasMultipleFaces: false,
      };
    }

    try {
      // Use TinyFaceDetector for better performance
      const detections = await faceapi
        .detectAllFaces(videoElement, new faceapi.TinyFaceDetectorOptions({
          inputSize: 416,
          scoreThreshold: this.options.minConfidence,
        }))
        .withFaceLandmarks();

      const faceCount = detections.length;
      const hasFace = faceCount === 1;
      const hasMultipleFaces = faceCount > 1;

      let result: FaceDetectionResult = {
        faceCount,
        hasFace,
        hasMultipleFaces,
      };

      // Add details for the first detected face
      if (detections.length > 0) {
        const detection = detections[0];
        result.confidence = detection.detection.score;
        result.boundingBox = {
          x: detection.detection.box.x,
          y: detection.detection.box.y,
          width: detection.detection.box.width,
          height: detection.detection.box.height,
        };
      }

      return result;
    } catch (error) {
      console.error('Error during face detection:', error);
      return {
        faceCount: 0,
        hasFace: false,
        hasMultipleFaces: false,
      };
    }
  }

  /**
   * Start continuous face detection on a video element
   */
  startContinuousDetection(
    videoElement: HTMLVideoElement,
    onDetection: (result: FaceDetectionResult) => void,
    onError?: (error: Error) => void
  ): void {
    if (this.detectionInterval) {
      this.stopContinuousDetection();
    }

    const detectLoop = async () => {
      try {
        const result = await this.detectFaces(videoElement);
        onDetection(result);
      } catch (error) {
        console.error('Face detection error:', error);
        if (onError) {
          onError(error as Error);
        }
      }
    };

    // Run initial detection
    detectLoop();

    // Set up interval for continuous detection
    this.detectionInterval = window.setInterval(detectLoop, this.options.detectionInterval);
  }

  /**
   * Stop continuous face detection
   */
  stopContinuousDetection(): void {
    if (this.detectionInterval) {
      clearInterval(this.detectionInterval);
      this.detectionInterval = null;
    }
  }

  /**
   * Check if the service is initialized
   */
  get initialized(): boolean {
    return this.isInitialized;
  }

  /**
   * Check if the service is currently loading
   */
  get loading(): boolean {
    return this.isLoading;
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.stopContinuousDetection();
  }
}

// Export a default instance
export const faceDetectionService = new FaceDetectionService();
