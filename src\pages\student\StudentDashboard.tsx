import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ClipboardList, LogOut, ChevronRight, CheckCircle, Clock } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useTest } from '../../contexts/TestContext';
import { TestResult } from '../../types/test';
import logoImage from '../../assets/images/logo.png';

const StudentDashboard = () => {
  const { currentUser, logout } = useAuth();
  const { prepareTest } = useTest();
  const navigate = useNavigate();
  
  const [previousTests, setPreviousTests] = useState<TestResult[]>([]);
  
  useEffect(() => {
    // Load previous test results
    const loadPreviousTests = () => {
      const savedResults = JSON.parse(localStorage.getItem('testResults') || '[]');
      if (currentUser) {
        // Filter results for current user
        const userResults = savedResults.filter(
          (result: TestResult) => result.userId === currentUser.id
        );
        setPreviousTests(userResults);
      }
    };
    
    loadPreviousTests();
  }, [currentUser]);
  
  const handleStartTest = () => {
    prepareTest();
    navigate('/test');
  };
  
  const handleLogout = () => {
    logout();
    navigate('/');
  };
  
  const getLatestResult = () => {
    if (previousTests.length === 0) return null;
    return previousTests.sort((a, b) => 
      new Date(b.testDate).getTime() - new Date(a.testDate).getTime()
    )[0];
  };
  
  const latestResult = getLatestResult();
  
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
          <div className="flex items-center">
            <img src={logoImage} alt="Kambaa's Test App Logo" className="h-10 w-auto mr-2" />
            <span className="text-xl font-semibold text-gray-900">Kambaa's Test App</span>
          </div>
          <button
            onClick={handleLogout}
            className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <LogOut className="mr-2 h-4 w-4" />
            Logout
          </button>
        </div>
      </div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white shadow rounded-lg overflow-hidden mb-6 flex flex-col items-center">
          <div className="px-6 py-5 border-b border-gray-200 w-full text-center">
            <h2 className="text-lg font-medium text-gray-900">
              Welcome, {currentUser?.name}
            </h2>
          </div>
          
          <div className="px-6 py-5 flex flex-col items-center">
            <div className="flex items-center space-x-4">
              <div className="flex-shrink-0">
                <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                  <span className="text-blue-600 font-medium text-lg">
                    {currentUser?.name.charAt(0)}
                  </span>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-900">{currentUser?.name}</h3>
                <p className="text-sm text-gray-500">
                  Roll Number: {currentUser?.rollNumber}
                </p>
                <p className="text-sm text-gray-500">
                  Mobile: {currentUser?.mobile}
                </p>
              </div>
            </div>
            
            <div className="mt-6 w-full max-w-md">
              <button
                onClick={handleStartTest}
                disabled={false} // Can be disabled based on test availability
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <ClipboardList className="mr-2 h-5 w-5" />
                Start New Test
              </button>
            </div>
          </div>
        </div>
        
        {previousTests.length === 0 && (
          <div className="bg-white shadow rounded-lg overflow-hidden text-center p-8">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100">
              <ClipboardList className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No test attempts yet</h3>
            <p className="mt-1 text-sm text-gray-500">
              You haven't taken any tests yet. Click the button above to start your first test.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default StudentDashboard;