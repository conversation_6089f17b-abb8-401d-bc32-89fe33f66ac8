import { Question } from '../types/test';

export const mockQuestions: Question[] = [
  // Logical Reasoning Questions
  {
    id: 'q1',
    text: 'If all Zings are Zongs, and all Zongs are Zangs, then all Zings are definitely Zangs.',
    options: [
      'True',
      'False',
      'Cannot be determined',
      'None of the above'
    ],
    correctAnswer: 'True',
    category: 'logical_reasoning'
  },
  {
    id: 'q2',
    text: 'Find the next number in the sequence: 2, 6, 12, 20, 30, ?',
    options: [
      '42',
      '40',
      '38',
      '36'
    ],
    correctAnswer: '42',
    category: 'logical_reasoning'
  },
  {
    id: 'q3',
    text: 'If 5 people can complete a work in 10 days, how many days will it take for 10 people to complete the same work?',
    options: [
      '5 days',
      '15 days',
      '20 days',
      '10 days'
    ],
    correctAnswer: '5 days',
    category: 'logical_reasoning'
  },
  {
    id: 'q4',
    text: 'A man walks 5 km toward south and then turns to the right. After walking 3 km he turns to the left and walks 5 km. Now in which direction is he from the starting place?',
    options: [
      'North',
      'South',
      'East',
      'West'
    ],
    correctAnswer: 'South-East',
    category: 'logical_reasoning'
  },
  {
    id: 'q5',
    text: 'A father said to his son, "I was as old as you are at the present at the time of your birth." If the father\'s age is 38 years now, what was the son\'s age five years back?',
    options: [
      '14 years',
      '19 years',
      '33 years',
      '38 years'
    ],
    correctAnswer: '14 years',
    category: 'logical_reasoning'
  },
  
  // Common Sense Questions
  {
    id: 'q6',
    text: 'What is the most appropriate action when you see a red traffic light?',
    options: [
      'Speed up to cross quickly',
      'Stop and wait for green light',
      'Look both ways and proceed',
      'Honk to alert other drivers'
    ],
    correctAnswer: 'Stop and wait for green light',
    category: 'common_sense'
  },
  {
    id: 'q7',
    text: 'If you find a wallet with money and identification, what should you do?',
    options: [
      'Keep the money and throw away the wallet',
      'Keep the wallet as a souvenir',
      'Return it to the owner or turn it in to authorities',
      'Take the money and mail back the ID'
    ],
    correctAnswer: 'Return it to the owner or turn it in to authorities',
    category: 'common_sense'
  },
  {
    id: 'q8',
    text: 'Which of the following is NOT a good practice during a job interview?',
    options: [
      'Arriving 10 minutes early',
      'Preparing questions about the company',
      'Checking your phone regularly',
      'Maintaining eye contact'
    ],
    correctAnswer: 'Checking your phone regularly',
    category: 'common_sense'
  },
  {
    id: 'q9',
    text: 'When cooking in the kitchen, why should you turn pot handles away from the front of the stove?',
    options: [
      'It makes the food cook faster',
      'It prevents accidental spills or burns',
      'It looks more organized',
      'It saves space on the stove'
    ],
    correctAnswer: 'It prevents accidental spills or burns',
    category: 'common_sense'
  },
  {
    id: 'q10',
    text: 'What should you do if your clothes catch fire?',
    options: [
      'Run as fast as possible',
      'Jump into water if available',
      'Stop, drop, and roll',
      'Take off your clothes immediately'
    ],
    correctAnswer: 'Stop, drop, and roll',
    category: 'common_sense'
  },
  
  // More questions can be added here to reach 1000
];