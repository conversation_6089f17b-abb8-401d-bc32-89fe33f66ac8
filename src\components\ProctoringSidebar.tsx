import { useState, useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { Camera, AlertTriangle, CheckCircle2, Video, Clock, Calendar, Timer } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { sendProctoringEvent } from '../utils/api';

interface ProctoringSidebarProps {
  violations: string[];
  onViolation: (type: string) => void;
  isTestEnded?: boolean;
}

// Define what methods will be exposed via the ref
export interface ProctoringSidebarHandle {
  captureAndSendSnapshot: (eventType: string, details: string) => Promise<void>;
}

const ProctoringSidebar = forwardRef<ProctoringSidebarHandle, ProctoringSidebarProps>(
  ({ violations, onViolation, isTestEnded = false }, ref) => {
    const { token } = useAuth();
  const [cameraActive, setCameraActive] = useState(false);
  const [cameraError, setCameraError] = useState('');
  const [faceDetected, setFaceDetected] = useState(false);
  const [multiplefaces, setMultipleFaces] = useState(false);
    const [isSubmittingProctorData, setIsSubmittingProctorData] = useState(false);
    const [photoSent, setPhotoSent] = useState(false);
    const [startTime, setStartTime] = useState<Date | null>(null);
    const [elapsedTime, setElapsedTime] = useState<{ hours: number; minutes: number; seconds: number }>({ 
      hours: 0, minutes: 0, seconds: 0 
    });

  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
    const streamRef = useRef<MediaStream | null>(null);
    const isInitialized = useRef(false);
    const startTimeSetRef = useRef(false); // Track if start time has been set

    // This reference will track if the initial photo has been sent
    const hasInitialPhotoBeenSent = useRef(false);
    // This reference will track if the final photo has been sent
    const hasFinalPhotoBeenSent = useRef(false);

    // Function to capture and send camera snapshot to API
    const captureAndSendSnapshot = async (eventType: string, details: string) => {
      console.log(`Attempting to capture and send ${eventType} screenshot`);
      
      if (!videoRef.current) {
        console.error('Cannot capture screenshot - video reference is null');
        return;
      }
      
      if (!canvasRef.current) {
        console.error('Cannot capture screenshot - canvas reference is null');
        return;
      }
      
      if (!token) {
        console.error('Cannot send screenshot - authentication token is missing');
        return;
      }
      
      if (isSubmittingProctorData) {
        console.log('Already submitting proctor data, skipping duplicate capture');
        return;
      }
      
      try {
        // Set flag to prevent multiple simultaneous attempts
        setIsSubmittingProctorData(true);
        
        // Create canvas same size as video
        const video = videoRef.current;
        const canvas = canvasRef.current;
        
        // Make sure video has dimensions before capturing
        if (video.videoWidth === 0 || video.videoHeight === 0) {
          console.error("Video not ready yet - no dimensions available");
          setIsSubmittingProctorData(false);
          return;
        }
        
        console.log(`Video dimensions: ${video.videoWidth}x${video.videoHeight}`);
        
        // Important: Create a temporary canvas instead of using the referenced one
        // This prevents affecting the video stream
        const tempCanvas = document.createElement('canvas');
        tempCanvas.width = video.videoWidth;
        tempCanvas.height = video.videoHeight;
        
        // Draw the current video frame to the temp canvas
        const ctx = tempCanvas.getContext('2d');
        if (ctx) {
          ctx.drawImage(video, 0, 0, tempCanvas.width, tempCanvas.height);
          console.log('Drew video frame to canvas');
          
          // Convert canvas to blob
          tempCanvas.toBlob(async (blob) => {
            if (blob && token) {
              try {
                console.log(`Converting to blob successful, size: ${blob.size} bytes`);
                
                // Using the exact format from the curl command:
                // -F 'event_type=face_not_detected'
                // -F 'details=Student face not visible in camera'
                // -F 'event_photo=@/path/to/your/image.jpg'
                // Data is sent as FormData, exactly like in the curl command
                const result = await sendProctoringEvent(
                  eventType,  // exact event type: 'test_started' or 'test_completed'
                  details,    // description
                  blob,       // the image blob from canvas
                  token       // JWT token for authentication
                );
                console.log(`Proctoring photo sent successfully. Event: ${eventType}`, result);
                
                // Mark appropriate flag based on event type
                if (eventType === 'test_started') {
                  hasInitialPhotoBeenSent.current = true;
                  setPhotoSent(true);
                } else if (eventType === 'test_completed') {
                  hasFinalPhotoBeenSent.current = true;
                }
                
              } catch (error) {
                console.error(`Failed to send proctoring photo for ${eventType}:`, error);
              } finally {
                setIsSubmittingProctorData(false);
                // Clean up temp canvas
                tempCanvas.remove();
              }
            } else {
              console.error('Blob or token is null, cannot send proctoring photo');
              setIsSubmittingProctorData(false);
              // Clean up temp canvas
              tempCanvas.remove();
            }
          }, 'image/jpeg', 0.9); // JPEG with 90% quality for better image
        } else {
          console.error('Could not get 2D context from canvas');
          setIsSubmittingProctorData(false);
        }
      } catch (error) {
        console.error('Error capturing photo:', error);
        setIsSubmittingProctorData(false);
      }
    };
    
    // Expose the captureAndSendSnapshot method via ref
    useImperativeHandle(ref, () => ({
      captureAndSendSnapshot
    }));

    // Initialize camera only once
    useEffect(() => {
      if (isInitialized.current || isTestEnded) return;

      const initializeCamera = async () => {
        try {
          console.log("Initializing camera for proctoring");
          
          // Ensure any previous stream is properly stopped
          if (streamRef.current) {
            streamRef.current.getTracks().forEach(track => {
              if (track.readyState === 'live') {
                track.stop();
              }
            });
            streamRef.current = null;
          }
          
          // Clear any existing srcObject
          if (videoRef.current && videoRef.current.srcObject) {
            videoRef.current.srcObject = null;
          }
          
          // Request camera with specific constraints for better compatibility
          const stream = await navigator.mediaDevices.getUserMedia({
            video: {
              width: { ideal: 1280 },
              height: { ideal: 720 },
              facingMode: "user"
            },
            audio: false // Explicitly set audio to false
          });
          
          streamRef.current = stream;
          
          // Set video element properties for better performance
          if (videoRef.current) {
            videoRef.current.srcObject = stream;
            videoRef.current.muted = true;
            videoRef.current.autoplay = true;
            videoRef.current.playsInline = true;
            
            // Ensure video is playing
            videoRef.current.play().catch(err => {
              console.error("Error playing video:", err);
            });
          }
          
          setCameraActive(true);
          setCameraError('');
          isInitialized.current = true;
          
          console.log("Camera initialized successfully");
          
          // Set start time ONLY ONCE and never update it again
          if (!startTimeSetRef.current) {
            const now = new Date();
            setStartTime(now);
            startTimeSetRef.current = true;
            
            // Initialize elapsed time from this point
            setElapsedTime({ hours: 0, minutes: 0, seconds: 0 });
          }
          
          // Send an initial camera snapshot when test starts - using a timeout to ensure video is ready
          setTimeout(() => {
            // Use ref to strictly ensure we only send one initial photo
            if (!hasInitialPhotoBeenSent.current && videoRef.current && videoRef.current.readyState >= 2) {
              captureAndSendSnapshot('test_started', 'Initial student verification photo');
              hasInitialPhotoBeenSent.current = true;
              setPhotoSent(true);
              console.log("Initial photo captured and queued for sending");
            } else if (!hasInitialPhotoBeenSent.current) {
              // Try again after another delay if video isn't ready yet
              console.log("Video not ready for initial snapshot, trying again in 2 seconds");
              setTimeout(() => {
                if (!hasInitialPhotoBeenSent.current && videoRef.current) {
                  captureAndSendSnapshot('test_started', 'Initial student verification photo');
                  hasInitialPhotoBeenSent.current = true;
                  setPhotoSent(true);
                }
              }, 2000);
            }
          }, 3000); // Give camera more time to initialize
        } catch (err) {
          console.error('Error accessing media devices:', err);
          setCameraActive(false);
          setCameraError('Failed to access camera');
          onViolation('device_access_failed');
        }
      };

      // Try to initialize camera and retry after a delay if it fails
      initializeCamera().catch(err => {
        console.error("Failed to initialize camera, will retry:", err);
        // Retry once after a delay
        setTimeout(() => {
          if (!isInitialized.current && !isTestEnded) {
            console.log("Retrying camera initialization...");
            initializeCamera();
          }
        }, 3000);
      });

      return () => {
        if (streamRef.current) {
          streamRef.current.getTracks().forEach(track => track.stop());
          streamRef.current = null;
        }
        isInitialized.current = false;
        // Don't reset startTimeSetRef to ensure start time is preserved
      };
    }, [onViolation, isTestEnded]);

    // Update elapsed time every second when test is active - completely separate from start time
    useEffect(() => {
      if (!startTime || isTestEnded) return;
      
      const calculateElapsedTime = () => {
        const now = new Date();
        const diff = Math.floor((now.getTime() - startTime.getTime()) / 1000); // total seconds
        
        const hours = Math.floor(diff / 3600);
        const minutes = Math.floor((diff % 3600) / 60);
        const seconds = diff % 60;
        
        setElapsedTime({ hours, minutes, seconds });
      };
      
      // Calculate immediately
      calculateElapsedTime();
      
      // Then update every second
      const intervalId = setInterval(calculateElapsedTime, 1000);
      
      return () => clearInterval(intervalId);
    }, [startTime, isTestEnded]);

    // Handle test end - capture final photo and shutdown camera
    useEffect(() => {
      if (isTestEnded && streamRef.current && !hasFinalPhotoBeenSent.current) {
        console.log('Test ended, attempting to capture final screenshot');
        
        // Capture the final screenshot before stopping the camera
        // captureAndSendSnapshot('test_completed', 'Final verification photo at test completion');
        hasFinalPhotoBeenSent.current = true;
        console.log('Captured final screenshot at test completion');
        
        // Stop all tracks after a small delay to ensure the photo is captured
        setTimeout(() => {
          if (streamRef.current) {
            streamRef.current.getTracks().forEach(track => {
              if (track.readyState === 'live') {
                track.stop();
                console.log(`Stopped ${track.kind} track due to test end`);
              }
            });
            streamRef.current = null;
            setCameraActive(false);
            isInitialized.current = false;
            
            // Also clear the video element's srcObject
            if (videoRef.current) {
              videoRef.current.srcObject = null;
            }
          }
        }, 1000); // Give time for the final photo to be captured and sent
      }
    }, [isTestEnded, token]);

    // Function to periodically check camera status
  useEffect(() => {
      // Don't check if test is ended
      if (isTestEnded) return;
      
      // Only start checks if camera has been initialized
      if (!cameraActive) return;

      // Flag to track if a camera_disabled violation was already reported
      let cameraDisabledReported = false;
      
      // Limit violation reports to once per check cycle
      let violationsThisCycle = new Set<string>();

      const checkVideoConnection = () => {
        // Reset violations for this cycle
        violationsThisCycle.clear();
        
        // 1. Verify if video element has a valid srcObject
        if (!videoRef.current || !videoRef.current.srcObject) {
          setCameraActive(false);
          setCameraError('Camera disconnected');
          
          // Only report camera_disabled once
          if (!cameraDisabledReported) {
            onViolation('camera_disabled');
            cameraDisabledReported = true;
          }
          return;
        }
        
        // 2. Check if we have tracks and they are active
        const videoStream = videoRef.current.srcObject as MediaStream;
        const videoTracks = videoStream.getVideoTracks();
        
        if (videoTracks.length === 0) {
          setCameraActive(false);
          setCameraError('No camera track found');
          
          // Only report camera_disabled once
          if (!cameraDisabledReported) {
            onViolation('camera_disabled');
            cameraDisabledReported = true;
          }
          return;
        }
        
        // Verify main video track is active
        const mainVideoTrack = videoTracks[0];
        if (!mainVideoTrack.enabled || mainVideoTrack.readyState !== 'live') {
          setCameraActive(false);
          setCameraError('Camera track disabled or not live');
          
          // Only report camera_disabled once
          if (!cameraDisabledReported) {
            onViolation('camera_disabled');
            cameraDisabledReported = true;
          }
          return;
        }
        
        // If we reached here, camera is active
        setCameraActive(true);
        setCameraError('');
        cameraDisabledReported = false;
        
        // Face detection simulation - reduced frequency (1% chance instead of 10%)
        // In a real implementation, this would use proper face detection
        if (Math.random() > 0.99 && !violationsThisCycle.has('no_face')) {
          setFaceDetected(false);
          onViolation('no_face');
          violationsThisCycle.add('no_face');
        } else {
          setFaceDetected(true);
        }
        
        // Multiple faces simulation - reduced frequency (0.5% chance instead of 5%)
        if (Math.random() > 0.995 && !violationsThisCycle.has('multiple_faces')) {
          setMultipleFaces(true);
          onViolation('multiple_faces');
          violationsThisCycle.add('multiple_faces');
        } else {
          setMultipleFaces(false);
        }
        
        // No background noise detection since we're not using microphone
      };
      
      // Run check immediately but with a small delay to ensure camera is properly initialized
      const initialCheckTimeout = setTimeout(checkVideoConnection, 2000);
      
      // Then run check less frequently (every 30 seconds instead of 10)
      const interval = setInterval(checkVideoConnection, 30000);
      
      return () => {
        clearTimeout(initialCheckTimeout);
        clearInterval(interval);
      };
    }, [cameraActive, isTestEnded, onViolation]);

    // Format time function
    const formatTime = (date: Date) => {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
    };

    // Format date function
    const formatDate = (date: Date) => {
      return date.toLocaleDateString([], { year: 'numeric', month: 'short', day: 'numeric' });
    };
    
    // Format duration function - displays hours:minutes:seconds
    const formatDuration = (time: { hours: number; minutes: number; seconds: number }) => {
      const { hours, minutes, seconds } = time;
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    };

  return (
      <div className="w-80 bg-white border-l border-gray-200 p-4">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Proctoring Status</h3>
      
        {/* Remove camera preview and replace with time info */}
        <div className="mb-6 bg-gray-50 rounded-lg p-4">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <Calendar className="h-5 w-5 text-blue-500" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700">Start Time</p>
                <p className="text-sm text-gray-900">
                  {startTime ? formatTime(startTime) : 'Not started'}
                </p>
                <p className="text-xs text-gray-500">
                  {startTime ? formatDate(startTime) : ''}
                </p>
          </div>
      </div>
      
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <Timer className="h-5 w-5 text-blue-500" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700">Recording Time</p>
                <p className="text-sm text-gray-900 font-mono">
                  {formatDuration(elapsedTime)}
                </p>
                <p className="text-xs text-gray-500">
                  {cameraActive ? 'Recording in progress' : 'Recording stopped'}
                </p>
              </div>
          </div>
        </div>
        
          {/* Show monitoring status message */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <p className="text-sm font-medium text-gray-700 mb-1">Monitoring Status</p>
          <div className="flex items-center">
              <div className={`h-3 w-3 rounded-full mr-2 ${cameraActive ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`}></div>
              <p className="text-sm text-gray-600">
                {cameraActive ? 'Actively monitoring your test' : 'Monitoring inactive'}
              </p>
            </div>
          </div>
        </div>
        
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              {cameraActive ? (
                <Video className="h-5 w-5 text-green-500" />
            ) : (
                <Video className="h-5 w-5 text-gray-400" />
            )}
          </div>
            <div>
              <p className="text-sm font-medium text-gray-900">Camera</p>
              <p className="text-sm text-gray-500">{cameraActive ? 'Active' : 'Inactive'}</p>
          </div>
          </div>
        </div>
        {violations.length > 0 && (
          <div className="mt-6">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Recent Violations</h4>
            <div className="space-y-2">
              {violations.map((violation, index) => (
                <div key={index} className="flex items-start space-x-2">
                  <AlertTriangle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
                  <p className="text-sm text-gray-700">{violation}</p>
                </div>
              ))}
        </div>
      </div>
        )}
        
        {/* Hidden video and canvas elements - still needed for capturing but not displayed */}
        <video
          ref={videoRef}
          autoPlay
          playsInline
          muted
          style={{ display: 'none' }}
        />
        <canvas ref={canvasRef} style={{ display: 'none' }} />
      </div>
    );
  }
  );

export default ProctoringSidebar;