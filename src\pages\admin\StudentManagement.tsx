import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  Plus, 
  Search, 
  Trash2, 
  Edit, 
  Save, 
  X,
  Download,
  Upload,
  User,
  Mail,
  Phone,
  ClipboardList,
  Check
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { StudentData, addStudent, getStudents, updateStudent, deleteStudent } from '../../utils/api';
import toast from 'react-hot-toast';

interface Student {
  id: string;
  name: string;
  rollNumber: string;
  mobile: string;
  email: string;
}

interface StudentFromAPI {
  id: number;
  name: string;
  college_roll_number: string;
  mobile_number: string;
  email: string;
}

// Add Modal component for popups
const Modal = ({ isOpen, onClose, title, children }) => {
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onClick={onClose}></div>
        <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
        <div className="inline-block overflow-hidden text-left align-bottom transition-all transform bg-white rounded-lg shadow-xl sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="px-4 pt-5 pb-4 bg-white sm:p-6 sm:pb-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium leading-6 text-gray-900">{title}</h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-500 focus:outline-none"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

const StudentManagement = () => {
  const navigate = useNavigate();
  const { token } = useAuth();
  
  const [students, setStudents] = useState<Student[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [editFormData, setEditFormData] = useState<Student | null>(null);
  
  // New student form - removed password field
  const [newStudent, setNewStudent] = useState({
    name: '',
    rollNumber: '',
    mobile: '',
    email: '',
  });
  
  // Transform API student format to our internal format
  const transformAPIStudent = (apiStudent: StudentFromAPI): Student => ({
    id: apiStudent.id.toString(),
    name: apiStudent.name,
    rollNumber: apiStudent.college_roll_number,
    mobile: apiStudent.mobile_number,
    email: apiStudent.email,
  });
  
  // Fetch students from API
  const fetchStudents = async () => {
    try {
      if (!token) return;
      
      setLoading(true);
      const response = await getStudents(token);
      
      // Handle the response based on the actual structure returned by the API
      const studentsList = Array.isArray(response) ? response : response.students || [];
      const transformedStudents = studentsList.map(transformAPIStudent);
      
      setStudents(transformedStudents);
    } catch (error) {
      console.error('Error fetching students:', error);
      toast.error('Failed to load students');
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    fetchStudents();
  }, [token]);
  
  // Default to local storage if API is not available
  useEffect(() => {
    if (students.length === 0 && !loading) {
      const savedStudents = JSON.parse(localStorage.getItem('students') || '[]');
      if (savedStudents.length > 0) {
        setStudents(savedStudents);
      }
    }
  }, [students.length, loading]);
  
  // Save students to localStorage as a backup
  useEffect(() => {
    if (students.length > 0) {
      localStorage.setItem('students', JSON.stringify(students));
    }
  }, [students]);
  
  const handleAddStudent = async () => {
    if (!newStudent.name || !newStudent.rollNumber || !newStudent.mobile || !newStudent.email) {
      toast.error('Please fill in all required fields');
      return;
    }
    
    // Check if roll number already exists
    if (students.some(student => student.rollNumber === newStudent.rollNumber)) {
      toast.error('A student with this roll number already exists');
      return;
    }
    
    setLoading(true);
    
    try {
      if (token) {
        // Use API if we have a token
        const studentData: StudentData = {
          name: newStudent.name,
          college_roll_number: newStudent.rollNumber,
          mobile_number: newStudent.mobile,
          email: newStudent.email,
        };
        
        const response = await addStudent(studentData, token);
        
        // Add the new student to the list with appropriate format
        const newStudentWithId: Student = {
          id: response.id?.toString() || `student-${Date.now()}`,
          name: newStudent.name,
          rollNumber: newStudent.rollNumber,
          mobile: newStudent.mobile,
          email: newStudent.email
        };
        
        setStudents([...students, newStudentWithId]);
      } else {
        // Fallback to local storage if no token
        const student: Student = {
          id: `student-${Date.now()}`,
          name: newStudent.name,
          rollNumber: newStudent.rollNumber,
          mobile: newStudent.mobile,
          email: newStudent.email
        };
        
        setStudents([...students, student]);
      }
      
      // Reset form
      setNewStudent({
        name: '',
        rollNumber: '',
        mobile: '',
        email: '',
      });
      
      setShowAddModal(false);
      toast.success('Student added successfully');
      
      // Refresh the student list
      fetchStudents();
    } catch (error) {
      console.error('Error adding student:', error);
      toast.error('Failed to add student');
    } finally {
      setLoading(false);
    }
  };
  
  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!editFormData) return;
    
    const { name, value } = e.target;
    setEditFormData({
      ...editFormData,
      [name]: value
    });
  };

  const startEditing = (student: Student) => {
    setEditFormData({ ...student });
    setShowEditModal(true);
  };

  const cancelEditing = () => {
    setShowEditModal(false);
    setEditFormData(null);
  };
  
  const submitEdit = async () => {
    if (!editFormData) return;
    
    try {
      if (token) {
        // Transform data to API format
        const apiData: Partial<StudentData> = {
          name: editFormData.name,
          email: editFormData.email,
          mobile_number: editFormData.mobile,
          college_roll_number: editFormData.rollNumber,
        };
        
        await updateStudent(editFormData.id, apiData, token);
      }
      
      // Update local state
      setStudents(students.map(student => 
        student.id === editFormData.id ? { ...editFormData } : student
      ));
      
      setShowEditModal(false);
      setEditFormData(null);
      toast.success('Student updated successfully');
    } catch (error) {
      console.error('Error updating student:', error);
      toast.error('Failed to update student');
    }
  };
  
  const handleDeleteStudent = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this student?')) {
      try {
        if (token) {
          await deleteStudent(id, token);
        }
        
        setStudents(students.filter(student => student.id !== id));
        toast.success('Student deleted successfully');
      } catch (error) {
        console.error('Error deleting student:', error);
        toast.error('Failed to delete student');
      }
    }
  };
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewStudent({
      ...newStudent,
      [name]: value
    });
  };
  
  const filteredStudents = students.filter(student => 
    student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.rollNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.mobile.includes(searchTerm)
  );
  
  const handleExportCSV = () => {
    // Create CSV content
    const csvContent = [
      ['Name', 'Roll Number', 'Mobile', 'Email'],
      ...students.map(student => [
        student.name,
        student.rollNumber,
        student.mobile,
        student.email
      ])
    ].map(row => row.join(',')).join('\n');
    
    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.setAttribute('hidden', '');
    a.setAttribute('href', url);
    a.setAttribute('download', 'students.csv');
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    toast.success('Students data exported successfully');
  };
  
  const handleImportCSV = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = (event) => {
      const csvText = event.target?.result as string;
      const rows = csvText.split('\n');
      
      // Skip header row
      const newStudents: Student[] = [];
      let hasErrors = false;
      
      for (let i = 1; i < rows.length; i++) {
        if (!rows[i].trim()) continue;
        
        const cols = rows[i].split(',');
        if (cols.length < 3) {
          hasErrors = true;
          continue;
        }
        
        const student = {
          id: `student-${Date.now()}-${i}`,
          name: cols[0].trim(),
          rollNumber: cols[1].trim(),
          mobile: cols[2].trim(),
          email: cols[3]?.trim() || ''
        };
        
        if (!student.name || !student.rollNumber || !student.mobile) {
          hasErrors = true;
          continue;
        }
        
        newStudents.push(student);
      }
      
      if (newStudents.length > 0) {
        setStudents(prev => [...prev, ...newStudents]);
        toast.success(`Imported ${newStudents.length} students successfully`);
      }
      
      if (hasErrors) {
        toast.error('Some rows had errors and were skipped');
      }
    };
    
    reader.readAsText(file);
  };
  
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex items-center">
          <button
            onClick={() => navigate('/admin')}
            className="mr-4 text-gray-400 hover:text-gray-500"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <h1 className="text-xl font-semibold text-gray-900">Student Management</h1>
        </div>
      </div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-2 sm:space-y-0">
            <div className="relative rounded-md w-full sm:w-64">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Search students..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <div className="flex space-x-2 w-full sm:w-auto">
              <button
                onClick={() => setShowAddModal(true)}
                className="flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Student
              </button>
              
              <button
                onClick={handleExportCSV}
                className="flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Download className="h-4 w-4 mr-1" />
                Export CSV
              </button>
              
              <label className="flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer">
                <Upload className="h-4 w-4 mr-1" />
                Import CSV
                <input
                  type="file"
                  accept=".csv"
                  className="hidden"
                  onChange={handleImportCSV}
                />
              </label>
            </div>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Roll Number
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Mobile
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Email
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {loading && filteredStudents.length === 0 ? (
                  <tr>
                    <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                      <div className="flex justify-center">
                        <svg className="animate-spin h-5 w-5 text-blue-500" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      </div>
                    </td>
                  </tr>
                ) : filteredStudents.length > 0 ? (
                  filteredStudents.map((student) => (
                    <tr key={student.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{student.name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{student.rollNumber}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{student.mobile}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{student.email || '-'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <button
                            onClick={() => startEditing(student)}
                            className="text-indigo-600 hover:text-indigo-900"
                            title="Edit student"
                          >
                            <Edit className="h-5 w-5" />
                          </button>
                          <button
                            onClick={() => handleDeleteStudent(student.id)}
                            className="text-red-600 hover:text-red-900"
                            title="Delete student"
                          >
                            <Trash2 className="h-5 w-5" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                      No students found. {searchTerm ? 'Try a different search term.' : 'Add a student to get started.'}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          
          <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 text-sm text-gray-500">
            Showing {filteredStudents.length} of {students.length} students
          </div>
        </div>
      </div>
      
      {/* Add Student Modal */}
      <Modal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        title="Add New Student"
      >
        <div className="space-y-4">
          {/* Name Field */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Full Name
            </label>
            <div className="flex items-center bg-white rounded-lg border border-gray-300 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 overflow-hidden">
              <div className="pl-3 pr-2">
                <User className="h-5 w-5 text-gray-400" aria-hidden="true" />
              </div>
              <input
                type="text"
                id="name"
                name="name"
                value={newStudent.name}
                onChange={handleInputChange}
                className="block w-full py-2 px-2 bg-transparent border-0 focus:ring-0 text-gray-900"
                placeholder="Enter student's full name"
                required
              />
            </div>
          </div>
          
          {/* Roll Number Field */}
          <div>
            <label htmlFor="rollNumber" className="block text-sm font-medium text-gray-700 mb-1">
              College Roll Number
            </label>
            <div className="flex items-center bg-white rounded-lg border border-gray-300 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 overflow-hidden">
              <div className="pl-3 pr-2">
                <ClipboardList className="h-5 w-5 text-gray-400" aria-hidden="true" />
              </div>
              <input
                type="text"
                id="rollNumber"
                name="rollNumber"
                value={newStudent.rollNumber}
                onChange={handleInputChange}
                className="block w-full py-2 px-2 bg-transparent border-0 focus:ring-0 text-gray-900"
                placeholder="Enter college roll number"
                required
              />
            </div>
          </div>
          
          {/* Mobile Number Field */}
          <div>
            <label htmlFor="mobile" className="block text-sm font-medium text-gray-700 mb-1">
              Mobile Number
            </label>
            <div className="flex items-center bg-white rounded-lg border border-gray-300 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 overflow-hidden">
              <div className="pl-3 pr-2">
                <Phone className="h-5 w-5 text-gray-400" aria-hidden="true" />
              </div>
              <input
                type="tel"
                id="mobile"
                name="mobile"
                value={newStudent.mobile}
                onChange={handleInputChange}
                className="block w-full py-2 px-2 bg-transparent border-0 focus:ring-0 text-gray-900"
                placeholder="Enter mobile number"
                required
              />
            </div>
          </div>
          
          {/* Email Field */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email Address
            </label>
            <div className="flex items-center bg-white rounded-lg border border-gray-300 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 overflow-hidden">
              <div className="pl-3 pr-2">
                <Mail className="h-5 w-5 text-gray-400" aria-hidden="true" />
              </div>
              <input
                type="email"
                id="email"
                name="email"
                value={newStudent.email}
                onChange={handleInputChange}
                className="block w-full py-2 px-2 bg-transparent border-0 focus:ring-0 text-gray-900"
                placeholder="Enter email address"
                required
              />
            </div>
          </div>
        </div>
        
        <div className="mt-6 flex justify-end space-x-3">
          <button
            onClick={() => setShowAddModal(false)}
            className="px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          
          <button
            onClick={handleAddStudent}
            disabled={loading}
            className="px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {loading ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Adding...
              </span>
            ) : (
              <span className="flex items-center">
                <Plus className="mr-2 h-4 w-4" />
                Add Student
              </span>
            )}
          </button>
        </div>
      </Modal>
      
      {/* Edit Student Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={cancelEditing}
        title="Edit Student"
      >
        {editFormData && (
          <div className="space-y-4">
            {/* Name Field */}
            <div>
              <label htmlFor="edit-name" className="block text-sm font-medium text-gray-700 mb-1">
                Full Name
              </label>
              <div className="flex items-center bg-white rounded-lg border border-gray-300 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 overflow-hidden">
                <div className="pl-3 pr-2">
                  <User className="h-5 w-5 text-gray-400" aria-hidden="true" />
                </div>
                <input
                  type="text"
                  id="edit-name"
                  name="name"
                  value={editFormData.name}
                  onChange={handleEditInputChange}
                  className="block w-full py-2 px-2 bg-transparent border-0 focus:ring-0 text-gray-900"
                  placeholder="Enter student's full name"
                  required
                />
              </div>
            </div>
            
            {/* Roll Number Field */}
            <div>
              <label htmlFor="edit-rollNumber" className="block text-sm font-medium text-gray-700 mb-1">
                College Roll Number
              </label>
              <div className="flex items-center bg-white rounded-lg border border-gray-300 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 overflow-hidden">
                <div className="pl-3 pr-2">
                  <ClipboardList className="h-5 w-5 text-gray-400" aria-hidden="true" />
                </div>
                <input
                  type="text"
                  id="edit-rollNumber"
                  name="rollNumber"
                  value={editFormData.rollNumber}
                  onChange={handleEditInputChange}
                  className="block w-full py-2 px-2 bg-transparent border-0 focus:ring-0 text-gray-900"
                  placeholder="Enter college roll number"
                  required
                />
              </div>
            </div>
            
            {/* Mobile Number Field */}
            <div>
              <label htmlFor="edit-mobile" className="block text-sm font-medium text-gray-700 mb-1">
                Mobile Number
              </label>
              <div className="flex items-center bg-white rounded-lg border border-gray-300 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 overflow-hidden">
                <div className="pl-3 pr-2">
                  <Phone className="h-5 w-5 text-gray-400" aria-hidden="true" />
                </div>
                <input
                  type="tel"
                  id="edit-mobile"
                  name="mobile"
                  value={editFormData.mobile}
                  onChange={handleEditInputChange}
                  className="block w-full py-2 px-2 bg-transparent border-0 focus:ring-0 text-gray-900"
                  placeholder="Enter mobile number"
                  required
                />
              </div>
            </div>
            
            {/* Email Field */}
            <div>
              <label htmlFor="edit-email" className="block text-sm font-medium text-gray-700 mb-1">
                Email Address
              </label>
              <div className="flex items-center bg-white rounded-lg border border-gray-300 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 overflow-hidden">
                <div className="pl-3 pr-2">
                  <Mail className="h-5 w-5 text-gray-400" aria-hidden="true" />
                </div>
                <input
                  type="email"
                  id="edit-email"
                  name="email"
                  value={editFormData.email}
                  onChange={handleEditInputChange}
                  className="block w-full py-2 px-2 bg-transparent border-0 focus:ring-0 text-gray-900"
                  placeholder="Enter email address"
                  required
                />
              </div>
            </div>
          </div>
        )}
        
        <div className="mt-6 flex justify-end space-x-3">
          <button
            onClick={cancelEditing}
            className="px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          
          <button
            onClick={submitEdit}
            className="px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <span className="flex items-center">
              <Check className="mr-2 h-4 w-4" />
              Save Changes
            </span>
          </button>
        </div>
      </Modal>
    </div>
  );
};

export default StudentManagement;