import { useEffect, useMemo } from 'react';
import { Clock } from 'lucide-react';

interface TestTimerProps {
  remainingTime: number;
  onTimeEnd: () => void;
}

const TestTimer = ({ remainingTime, onTimeEnd }: TestTimerProps) => {
  // Format time as MM:SS
  const formattedTime = useMemo(() => {
    const minutes = Math.floor(remainingTime / 60);
    const seconds = remainingTime % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, [remainingTime]);
  
  // Calculate percentage of time remaining
  const timePercentage = useMemo(() => {
    return (remainingTime / (60 * 60)) * 100;
  }, [remainingTime]);
  
  // Determine color based on time remaining
  const timerColor = useMemo(() => {
    if (timePercentage > 50) return 'bg-green-500';
    if (timePercentage > 25) return 'bg-yellow-500';
    return 'bg-red-500';
  }, [timePercentage]);
  
  useEffect(() => {
    if (remainingTime <= 0) {
      onTimeEnd();
    }
  }, [remainingTime, onTimeEnd]);

  return (
    <div className="flex flex-col items-center px-3 sm:px-4 py-2 bg-white rounded-lg shadow w-full sm:w-auto">
      <div className="flex items-center justify-center w-full mb-1 sm:mb-2">
        <Clock size={14} className="mr-1 sm:mr-2 text-gray-600" />
        <span className="font-mono text-base sm:text-lg font-bold">{formattedTime}</span>
      </div>
      
      <div className="w-full h-1.5 sm:h-2 bg-gray-200 rounded-full overflow-hidden">
        <div 
          className={`h-full ${timerColor} transition-all duration-1000 ease-linear`} 
          style={{ width: `${timePercentage}%` }}
        />
      </div>
      
      <p className={`mt-1 sm:mt-2 text-xs ${remainingTime < 300 ? 'text-red-600 font-medium animate-pulse' : 'text-gray-500'}`}>
        {remainingTime < 300 
          ? 'Less than 5 minutes remaining!' 
          : 'Remaining time for the test'}
      </p>
    </div>
  );
};

export default TestTimer;