import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { AuthProvider } from './contexts/AuthContext';
import { TestProvider } from './contexts/TestContext';
import ProtectedRoute from './components/ProtectedRoute';
import StudentLoginPage from './pages/StudentLoginPage';
import AdminLoginPage from './pages/AdminLoginPage';
import RegisterPage from './pages/RegisterPage';
import StudentDashboard from './pages/student/StudentDashboard';
import TestPage from './pages/student/TestPage';
import ResultPage from './pages/student/ResultPage';
import AdminDashboard from './pages/admin/AdminDashboard';
import StudentManagement from './pages/admin/StudentManagement';
import QuestionManagement from './pages/admin/QuestionManagement';
import ResultManagement from './pages/admin/ResultManagement';
import CollegeManagement from './pages/admin/CollegeManagement';
import NotFoundPage from './pages/NotFoundPage';

function App() {
  return (
    <AuthProvider>
      <TestProvider>
        <Router>
          <Toaster position="top-center" />
          <Routes>
            {/* Student Auth Routes */}
            <Route path="/" element={<RegisterPage />} />
            <Route path="/student-login" element={<StudentLoginPage />} />
            <Route path="/admin-login" element={<AdminLoginPage />} />
            
            {/* Student Routes */}
            <Route 
              path="/student" 
              element={
                <ProtectedRoute role="student">
                  <StudentDashboard />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/test" 
              element={
                <ProtectedRoute role="student">
                  <TestPage />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/result" 
              element={
                <ProtectedRoute role="student">
                  <ResultPage />
                </ProtectedRoute>
              } 
            />
            
            {/* Admin Routes */}
            <Route 
              path="/admin" 
              element={
                <ProtectedRoute role="admin">
                  <AdminDashboard />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/admin/students" 
              element={
                <ProtectedRoute role="admin">
                  <StudentManagement />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/admin/questions" 
              element={
                <ProtectedRoute role="admin">
                  <QuestionManagement />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/admin/results" 
              element={
                <ProtectedRoute role="admin">
                  <ResultManagement />
                </ProtectedRoute>
              } 
            />
            
            <Route 
              path="/admin/colleges" 
              element={
                <ProtectedRoute role="admin">
                  <CollegeManagement />
                </ProtectedRoute>
              } 
            />
            
            {/* 404 Route */}
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </Router>
      </TestProvider>
    </AuthProvider>
  );
}

export default App;