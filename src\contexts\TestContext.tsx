import { createContext, useContext, useState, ReactNode, useEffect, useCallback } from 'react';
import { Question, TestResult, TestContextType } from '../types/test';
import { useAuth } from './AuthContext';
import { mockQuestions } from '../data/mockQuestions';
import { startTest, submitTest, TestQuestion } from '../utils/api';
import toast from 'react-hot-toast';
import { useNavigate } from 'react-router-dom';
const TestContext = createContext<TestContextType | undefined>(undefined);

export const useTest = () => {
  const context = useContext(TestContext);
  const navigate = useNavigate();
  if (context === undefined) {
    throw new Error('useTest must be used within a TestProvider');
  }
  return context;
};

interface TestProviderProps {
  children: ReactNode;
}

// Transform API question to UI format
const transformAPIQuestionToUI = (question: TestQuestion): Question => {
  // First map the correct_answer letter to the actual option text
  let correctAnswerText = "";
  if (question.correct_answer === 'A') correctAnswerText = question.option_a;
  else if (question.correct_answer === 'B') correctAnswerText = question.option_b;
  else if (question.correct_answer === 'C') correctAnswerText = question.option_c;
  else if (question.correct_answer === 'D') correctAnswerText = question.option_d;
  
  return {
    id: question.id.toString(),
    text: question.question_text,
    options: [
      question.option_a,
      question.option_b,
      question.option_c,
      question.option_d
    ],
    correctAnswer: correctAnswerText,
    category: question.question_type,
  };
};

export const TestProvider = ({ children }: TestProviderProps) => {
  const { currentUser, token } = useAuth();
  const [questions, setQuestions] = useState<Question[]>([]);
  const [activeTestId, setActiveTestId] = useState<number | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<{[key: number]: string}>({});
  const [testStarted, setTestStarted] = useState(false);
  const [testEnded, setTestEnded] = useState(false);
  const [remainingTime, setRemainingTime] = useState(60 * 60); // 60 minutes in seconds
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [loading, setLoading] = useState(false);
  
  // Load and prepare test
  const prepareTest = useCallback(() => {
    // Reset all test state
    setQuestions([]);
    setActiveTestId(null);
    setCurrentQuestionIndex(0);
    setAnswers({});
    setTestStarted(false);
    setTestEnded(false);
    setRemainingTime(60 * 60);
    setTestResult(null);
  }, []);
  
  // Start test by calling the API
  const startTestProcess = useCallback(async () => {
    if (!token) {
      toast.error('You need to be logged in to start a test');
      return [];
    }
    
    try {
      setLoading(true);
      const response = await startTest(token);
      
      // Transform API questions to UI format
      const transformedQuestions = response.questions.map(transformAPIQuestionToUI);
      
      setQuestions(transformedQuestions);
      setActiveTestId(response.test.id);
      setLoading(false);
      
      // Officially start the test timer and UI
      setTestStarted(true);
      const startTime = Date.now();
      localStorage.setItem('testStartTime', startTime.toString());
      localStorage.setItem('activeTestId', response.test.id.toString());
      
      return transformedQuestions;
    } catch (error) {
      setLoading(false);
      toast.error('You are already completed the test.');
      console.error('Error starting test:', error);
      navigate('/', { replace: true });
      return [];
    }
  }, [token]);
  
  // Answer question
  const answerQuestion = (questionIndex: number, answer: string) => {
    setAnswers(prev => ({
      ...prev,
      [questionIndex]: answer
    }));
  };
  
  // Navigate questions
  const goToNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };
  
  const goToPreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };
  
  const goToQuestion = (index: number) => {
    if (index >= 0 && index < questions.length) {
      setCurrentQuestionIndex(index);
    }
  };
  
  // End test, calculate results, and submit to API
  const endTest = useCallback(async (reason?: string) => {
    if (!testStarted || testEnded || !token || !activeTestId) return;
    
    setTestEnded(true);
    
    // Calculate score locally
    let correctAnswers = 0;
    const answersForApi = [];
    
    questions.forEach((question, index) => {
      // Get the user's answer for this question
      const userAnswer = answers[index];
      if (!userAnswer) return;
      
      // Map UI answer (which is the full text) to A, B, C, D format
      let selectedOption = '';
      
      if (userAnswer === question.options[0]) selectedOption = 'A';
      else if (userAnswer === question.options[1]) selectedOption = 'B';
      else if (userAnswer === question.options[2]) selectedOption = 'C';
      else if (userAnswer === question.options[3]) selectedOption = 'D';
      
      if (selectedOption) {
        // Format for API as shown in the curl example
        answersForApi.push({
          question_id: question.id,
          selected_option: selectedOption
        });
      }
      
      // For UI display purposes
      if (userAnswer === question.correctAnswer) {
        correctAnswers++;
      }
    });
    
    // Try to submit to API
    try {
      setLoading(true);
      const response = await submitTest(
        activeTestId,
        answersForApi,
        token
      );
      setLoading(false);
      
      // Use the response or fallback to calculated values
      const score = reason === 'disqualified' ? 0 : (correctAnswers / questions.length) * 100;
      const newResult = {
        userId: currentUser?.id || '',
        userName: currentUser?.name || '',
        userRollNumber: currentUser?.rollNumber || '',
        userMobile: currentUser?.mobile || '',
        totalQuestions: questions.length,
        attemptedQuestions: Object.keys(answers).length,
        correctAnswers: reason === 'disqualified' ? 0 : correctAnswers,
        score,
        testDate: new Date().toISOString(),
        duration: 3600 - remainingTime,
        disqualified: reason === 'disqualified'
      };
      
      setTestResult(newResult);
      
      // Save result to localStorage
      const savedResults = JSON.parse(localStorage.getItem('testResults') || '[]');
      savedResults.push(newResult);
      localStorage.setItem('testResults', JSON.stringify(savedResults));
      
      // Clean up test data
      localStorage.removeItem('testStartTime');
      localStorage.removeItem('activeTestId');
      
      toast.success('Test submitted successfully');
      return newResult;
    } catch (error) {
      setLoading(false);
      console.error('Error submitting test:', error);
      toast.error('Failed to submit test results');
      
      // Return local calculations even if API fails
      const score = (correctAnswers / questions.length) * 100;
      const newResult = {
        userId: currentUser?.id || '',
        userName: currentUser?.name || '',
        userRollNumber: currentUser?.rollNumber || '',
        userMobile: currentUser?.mobile || '',
        totalQuestions: questions.length,
        attemptedQuestions: Object.keys(answers).length,
        correctAnswers,
        score,
        testDate: new Date().toISOString(),
        duration: 3600 - remainingTime,
        disqualified: reason === 'disqualified'
      };
      
      setTestResult(newResult);
      return newResult;
    }
  }, [testStarted, testEnded, questions, answers, remainingTime, currentUser, token, activeTestId]);
  
  // Timer logic
  useEffect(() => {
    if (!testStarted || testEnded) return;
    
    const timer = setInterval(() => {
      setRemainingTime(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          endTest();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    
    return () => clearInterval(timer);
  }, [testStarted, testEnded, endTest]);
  
  // Resume test if page is refreshed
  useEffect(() => {
    const startTimeStr = localStorage.getItem('testStartTime');
    const testIdStr = localStorage.getItem('activeTestId');
    
    if (startTimeStr && testIdStr && !testStarted && !testEnded) {
      const startTime = parseInt(startTimeStr, 10);
      const testId = parseInt(testIdStr, 10);
      const elapsedSeconds = Math.floor((Date.now() - startTime) / 1000);
      
      setActiveTestId(testId);
      
      if (elapsedSeconds < 3600) {
        setTestStarted(true);
        setRemainingTime(3600 - elapsedSeconds);
      } else {
        // Test should have ended
        setTestStarted(true);
        setRemainingTime(0);
        endTest();
      }
    }
  }, [testStarted, testEnded, endTest]);
  
  const value = {
    questions,
    currentQuestionIndex,
    answers,
    testStarted,
    testEnded,
    remainingTime,
    testResult,
    loading,
    prepareTest,
    startTest: startTestProcess,
    answerQuestion,
    goToNextQuestion,
    goToPreviousQuestion,
    goToQuestion,
    endTest,
  };
  
  return <TestContext.Provider value={value}>{children}</TestContext.Provider>;
};