export interface Question {
  id: string;
  text: string;
  options: string[];
  correctAnswer: string;
  category: string;
}

export interface TestResult {
  userId: string;
  userName: string;
  userRollNumber: string;
  userMobile: string;
  totalQuestions: number;
  attemptedQuestions: number;
  correctAnswers: number;
  score: number;
  testDate: string;
  duration: number;
  disqualified?: boolean;
}

export interface TestContextType {
  questions: Question[];
  currentQuestionIndex: number;
  answers: {[key: number]: string};
  testStarted: boolean;
  testEnded: boolean;
  remainingTime: number;
  testResult: TestResult | null;
  loading: boolean;
  prepareTest: () => void;
  startTest: () => Promise<Question[] | undefined>;
  answerQuestion: (questionIndex: number, answer: string) => void;
  goToNextQuestion: () => void;
  goToPreviousQuestion: () => void;
  goToQuestion: (index: number) => void;
  endTest: (reason?: string) => Promise<TestResult | undefined>;
}