import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { LogOut, Users, FileText, BarChart3, ChevronRight, Loader, Building } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { getAllTests, TestResult } from '../../utils/api';
import toast from 'react-hot-toast';
import logoImage from '../../assets/images/logo.png';

const AdminDashboard = () => {
  const { currentUser, logout, token } = useAuth();
  const navigate = useNavigate();
  
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalStudents: 0,
    totalQuestions: 0,
    testsCompleted: 0,
    averageScore: 0,
  });
  
  useEffect(() => {
    const fetchData = async () => {
      if (!token) return;
      
      try {
        setLoading(true);
        // Fetch test results from API
        const response = await getAllTests(token);
        const tests = response.tests || [];
        setTestResults(tests);
        
        // Calculate stats
        const totalStudents = new Set(tests.map(test => test.user_id)).size;
        // For questions, we don't have that info yet, so keeping it at 0 or fetch it separately
        const totalQuestions = 0;
        const testsCompleted = tests.filter(test => test.is_completed === 1).length;
        
        let totalScore = 0;
        tests.filter(test => test.is_completed === 1).forEach(test => {
          totalScore += test.score;
        });
        
        const averageScore = testsCompleted > 0 ? totalScore / testsCompleted : 0;
        
        setStats({
          totalStudents,
          totalQuestions,
          testsCompleted,
          averageScore,
        });
      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error('Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [token]);
  
  const handleLogout = () => {
    logout();
    navigate('/admin-login');
  };
  
  // Get recent test results (last 5)
  const recentTestResults = [...testResults]
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    .slice(0, 5);
  
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex flex-col items-center">
          <Loader className="h-8 w-8 text-blue-600 animate-spin" />
          <p className="mt-4 text-gray-600">Loading dashboard data...</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
          <div className="flex items-center">
            <img src={logoImage} alt="Kambaa's Test App Logo" className="h-10 w-auto mr-2" />
            <span className="text-xl font-semibold text-gray-900">Admin Dashboard</span>
          </div>
          <button
            onClick={handleLogout}
            className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <LogOut className="mr-2 h-4 w-4" />
            Logout
          </button>
        </div>
      </div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          <div className="lg:col-span-2 space-y-6">
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-6 py-5 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">
                  Overview
                </h2>
              </div>
              
              <div className="px-6 py-5">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-blue-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <Users className="h-5 w-5 text-blue-500" />
                      <h3 className="ml-2 text-sm font-medium text-blue-900">Students</h3>
                    </div>
                    <p className="mt-2 text-2xl font-semibold text-blue-900">{stats.totalStudents}</p>
                  </div>
                  
                  <div className="bg-purple-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <FileText className="h-5 w-5 text-purple-500" />
                      <h3 className="ml-2 text-sm font-medium text-purple-900">Questions</h3>
                    </div>
                    <p className="mt-2 text-2xl font-semibold text-purple-900">{stats.totalQuestions}</p>
                  </div>
                  
                  <div className="bg-green-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <BarChart3 className="h-5 w-5 text-green-500" />
                      <h3 className="ml-2 text-sm font-medium text-green-900">Tests</h3>
                    </div>
                    <p className="mt-2 text-2xl font-semibold text-green-900">{stats.testsCompleted}</p>
                  </div>
                  
                  <div className="bg-amber-50 rounded-lg p-4">
                    <div className="flex items-center">
                      <BarChart3 className="h-5 w-5 text-amber-500" />
                      <h3 className="ml-2 text-sm font-medium text-amber-900">Avg. Score</h3>
                    </div>
                    <p className="mt-2 text-2xl font-semibold text-amber-900">{stats.averageScore.toFixed(1)}%</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-6 py-5 border-b border-gray-200 flex justify-between items-center">
                <h2 className="text-lg font-medium text-gray-900">
                  Recent Test Results
                </h2>
                <button
                  onClick={() => navigate('/admin/results')}
                  className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
                >
                  View all
                  <ChevronRight className="ml-1 h-4 w-4" />
                </button>
              </div>
              
              <div className="overflow-x-auto">
                {recentTestResults.length > 0 ? (
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Student
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Score
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {recentTestResults.map((result, index) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="text-sm font-medium text-gray-900">
                                {result.name}
                              </div>
                              <div className="ml-2 text-xs text-gray-500">
                                {result.college_roll_number}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500">
                              {new Date(result.start_time).toLocaleDateString()}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              result.is_completed === 1
                                ? 'bg-green-100 text-green-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {result.is_completed === 1 ? 'Completed' : 'In Progress'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {result.is_completed === 1 ? `${result.score}` : '-'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                ) : (
                  <div className="text-center py-6">
                    <p className="text-gray-500">No test results available</p>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          <div className="space-y-6">
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-6 py-5 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">
                  Admin Controls
                </h2>
              </div>
              
              <div className="px-6 py-5 space-y-4">
                <button
                  onClick={() => navigate('/admin/students')}
                  className="w-full flex justify-between items-center px-4 py-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <div className="flex items-center">
                    <Users className="mr-3 h-5 w-5 text-gray-400" />
                    <span>Manage Students</span>
                  </div>
                  <ChevronRight className="h-5 w-5 text-gray-400" />
                </button>
                
                <button
                  onClick={() => navigate('/admin/questions')}
                  className="w-full flex justify-between items-center px-4 py-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <div className="flex items-center">
                    <FileText className="mr-3 h-5 w-5 text-gray-400" />
                    <span>Manage Questions</span>
                  </div>
                  <ChevronRight className="h-5 w-5 text-gray-400" />
                </button>
                
                <button
                  onClick={() => navigate('/admin/colleges')}
                  className="w-full flex justify-between items-center px-4 py-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <div className="flex items-center">
                    <Building className="mr-3 h-5 w-5 text-gray-400" />
                    <span>Manage Colleges</span>
                  </div>
                  <ChevronRight className="h-5 w-5 text-gray-400" />
                </button>
                
                <button
                  onClick={() => navigate('/admin/results')}
                  className="w-full flex justify-between items-center px-4 py-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <div className="flex items-center">
                    <BarChart3 className="mr-3 h-5 w-5 text-gray-400" />
                    <span>View Test Results</span>
                  </div>
                  <ChevronRight className="h-5 w-5 text-gray-400" />
                </button>
              </div>
            </div>
            
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-6 py-5 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">
                  Admin Information
                </h2>
              </div>
              
              <div className="px-6 py-5">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div className="h-10 w-10 rounded-full bg-blue-600 flex items-center justify-center">
                      <span className="text-white font-medium text-sm">
                        {currentUser?.name?.charAt(0) || 'A'}
                      </span>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-900">{currentUser?.name || 'Administrator'}</h3>
                    <p className="text-sm text-gray-500">
                      {currentUser?.email || '<EMAIL>'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;