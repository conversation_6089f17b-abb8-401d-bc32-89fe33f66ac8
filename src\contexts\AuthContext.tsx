import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, AuthContextType, LoginResponse } from '../types/auth';
import { AUTH_ENDPOINTS, apiRequest } from '../utils/api';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [token, setToken] = useState<string | null>(null);

  useEffect(() => {
    // Check for user and token in localStorage on initial load
    const storedUser = localStorage.getItem('testAppUser');
    const storedToken = localStorage.getItem('testAppToken');
    
    if (storedUser) {
      setCurrentUser(JSON.parse(storedUser));
    }
    
    if (storedToken) {
      setToken(storedToken);
    }
    
    setLoading(false);
  }, []);

  const loginStudent = async (mobileNumber: string, collegeRollNumber: string): Promise<boolean> => {
    try {
      const data: LoginResponse = await apiRequest(
        AUTH_ENDPOINTS.LOGIN_STUDENT,
        'POST',
        { 
          mobile_number: mobileNumber, 
          college_roll_number: collegeRollNumber,
          college_id: 1 // Default college ID if not known
        }
      );
      
      // Create user object from response
      const studentUser: User = {
        id: data.user.id.toString(),
        name: data.user.name || `Student ${collegeRollNumber}`,
        mobile: mobileNumber,
        rollNumber: collegeRollNumber,
        role: 'student',
        email: data.user.email || '',
      };

      // Save user data and token
      setCurrentUser(studentUser);
      setToken(data.token);
      localStorage.setItem('testAppUser', JSON.stringify(studentUser));
      localStorage.setItem('testAppToken', data.token);
      
      return true;
    } catch (error) {
      console.error('Student login error:', error);
      return false;
    }
  };

  const loginAdmin = async (email: string, password: string): Promise<boolean> => {
    try {
      const data: LoginResponse = await apiRequest(
        AUTH_ENDPOINTS.LOGIN_ADMIN,
        'POST',
        { email, password }
      );
      
      // Create user object from response
      const adminUser: User = {
        id: data.user.id.toString(),
        name: data.user.name || 'Administrator',
        mobile: data.user.mobile || '',
        rollNumber: '',
        email: data.user.email,
        role: 'admin',
      };

      // Save user data and token
      setCurrentUser(adminUser);
      setToken(data.token);
      localStorage.setItem('testAppUser', JSON.stringify(adminUser));
      localStorage.setItem('testAppToken', data.token);
      
      return true;
    } catch (error) {
      console.error('Admin login error:', error);
      return false;
    }
  };

  const login = async (identifier: string, password: string, role: 'student' | 'admin'): Promise<boolean> => {
    if (role === 'admin') {
      return loginAdmin(identifier, password);
    } else {
      return loginStudent(identifier, password);
    }
  };

  const logout = () => {
    setCurrentUser(null);
    setToken(null);
    localStorage.removeItem('testAppUser');
    localStorage.removeItem('testAppToken');
  };

  const value = {
    currentUser,
    token,
    login,
    logout,
    loading,
    setCurrentUser,
    setToken
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};