import { Check, AlertCircle } from 'lucide-react';

interface QuestionNavigationProps {
  totalQuestions: number;
  currentIndex: number;
  answeredQuestions: {[key: number]: string};
  onNavigate: (index: number) => void;
}

const QuestionNavigation = ({ 
  totalQuestions, 
  currentIndex, 
  answeredQuestions, 
  onNavigate 
}: QuestionNavigationProps) => {
  const isQuestionAnswered = (index: number) => {
    return index in answeredQuestions;
  };
  
  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <h3 className="text-sm font-medium text-gray-700 mb-3">Question Navigator</h3>
      
      <div className="grid grid-cols-5 gap-2">
        {Array.from({ length: totalQuestions }).map((_, index) => {
          const isActive = index === currentIndex;
          const isAnswered = isQuestionAnswered(index);
          
          return (
            <button
              key={index}
              onClick={() => onNavigate(index)}
              className={`
                h-10 flex items-center justify-center rounded
                transition-colors duration-200
                ${isActive 
                  ? 'bg-blue-600 text-white' 
                  : isAnswered 
                    ? 'bg-green-100 text-green-800 hover:bg-green-200' 
                    : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                }
              `}
            >
              {isAnswered ? (
                <Check size={16} />
              ) : (
                <span>{index + 1}</span>
              )}
            </button>
          );
        })}
      </div>
      
      <div className="mt-4 flex items-center justify-between text-sm text-gray-500">
        <div className="flex items-center">
          <div className="w-4 h-4 bg-green-100 rounded mr-1"></div>
          <span>Answered</span>
        </div>
        <div className="flex items-center">
          <div className="w-4 h-4 bg-gray-100 rounded mr-1"></div>
          <span>Not answered</span>
        </div>
        <div className="flex items-center">
          <div className="w-4 h-4 bg-blue-600 rounded mr-1"></div>
          <span>Current</span>
        </div>
      </div>
      
      <div className="mt-4 px-4 py-3 bg-blue-50 rounded-lg text-sm text-blue-800 flex items-start">
        <AlertCircle size={16} className="mr-2 flex-shrink-0 mt-0.5" />
        <p>You've answered {Object.keys(answeredQuestions).length} out of {totalQuestions} questions.</p>
      </div>
    </div>
  );
};

export default QuestionNavigation;