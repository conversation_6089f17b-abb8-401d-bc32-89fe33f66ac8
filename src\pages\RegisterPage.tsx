import { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { BookOpen, User, Lock, Mail, ArrowLeft, Phone, UserPlus, CheckCircle, Calendar } from 'lucide-react';
import toast from 'react-hot-toast';
import { registerStudent, StudentRegistrationData, StudentRegistrationResponse } from '../utils/api';
import { useAuth } from '../contexts/AuthContext';
import logoImage from '../assets/images/logo.png';

const RegisterPage = () => {
  // Clear localStorage when registration page is loaded
  useEffect(() => {
    // Clean up any existing storage data before registration
    localStorage.removeItem('testAppUser');
    localStorage.removeItem('testAppToken');
    localStorage.removeItem('tabChangeCount');
    console.log('Cleared localStorage on registration page load');
  }, []);

  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [mobileNumber, setMobileNumber] = useState('');
  const [collegeRollNumber, setCollegeRollNumber] = useState('');
  const [dob, setDob] = useState('');
  const [loading, setLoading] = useState(false);
  const [showSuccessPopup, setShowSuccessPopup] = useState(false);
  const [registeredName, setRegisteredName] = useState('');
  
  // Validation error states
  const [emailError, setEmailError] = useState('');
  const [mobileError, setMobileError] = useState('');
  const [rollNumberError, setRollNumberError] = useState('');
  const [dobError, setDobError] = useState('');
  
  const navigate = useNavigate();
  const { login, setCurrentUser, setToken } = useAuth();
  
  // Validate email format
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValid = emailRegex.test(email);
    setEmailError(isValid ? '' : 'Please enter a valid email address');
    return isValid;
  };
  
  // Validate mobile number (exactly 10 digits)
  const validateMobile = (mobile: string): boolean => {
    const mobileRegex = /^[0-9]{10}$/;
    const isValid = mobileRegex.test(mobile);
    setMobileError(isValid ? '' : `Mobile number must be exactly 10 digits (numbers only). Current: ${mobile.length}/10`);
    return isValid;
  };
  
  // Validate college roll number (exactly 12 characters)
  const validateRollNumber = (rollNumber: string): boolean => {
    const isValid = rollNumber.length === 12;
    setRollNumberError(isValid ? '' : `College roll number must be exactly 12 characters. Current: ${rollNumber.length}/12`);
    return isValid;
  };
  
  // Validate date of birth (must be in the past and user must be at least 15 years old)
  const validateDob = (dateStr: string): boolean => {
    if (!dateStr) {
      setDobError('');
      return true;
    }
    
    try {
      const selectedDate = new Date(dateStr);
      const today = new Date();
      
      // Check if date is in the past
      if (selectedDate > today) {
        setDobError('Date of birth must be in the past');
        return false;
      }
      
      // Calculate age
      const ageDifMs = today.getTime() - selectedDate.getTime();
      const ageDate = new Date(ageDifMs);
      const age = Math.abs(ageDate.getUTCFullYear() - 1970);
      
      // Check if user is at least 15 years old
      if (age < 15) {
        setDobError('You must be at least 15 years old');
        return false;
      }
      
      setDobError('');
      return true;
    } catch (e) {
      setDobError('Please enter a valid date');
      return false;
    }
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Reset validation errors
    setEmailError('');
    setMobileError('');
    setRollNumberError('');
    setDobError('');
    
    // Validate form inputs
    if (!name || !email || !mobileNumber || !collegeRollNumber) {
      toast.error('Please fill in all required fields');
      
      // Set specific validation errors for empty fields
      if (!name) toast.error('Please enter your full name');
      if (!email) setEmailError('Email is required');
      if (!mobileNumber) setMobileError('Mobile number is required');
      if (!collegeRollNumber) setRollNumberError('College roll number is required');
      return;
    }
    
    // Perform validations
    const isEmailValid = validateEmail(email);
    const isMobileValid = validateMobile(mobileNumber);
    const isRollNumberValid = validateRollNumber(collegeRollNumber);
    const isDobValid = validateDob(dob);
    
    // Stop submission if any validation fails
    if (!isEmailValid || !isMobileValid || !isRollNumberValid || !isDobValid) {
      // Show specific error toasts for better visibility
      if (!isEmailValid) toast.error('Please enter a valid email address');
      if (!isMobileValid) toast.error('Mobile number must be exactly 10 digits (numbers only)');
      if (!isRollNumberValid) toast.error('College roll number must be exactly 12 characters');
      if (!isDobValid) toast.error(dobError);
      return;
    }
    
    // Double check mobile number format - must be exactly 10 digits
    if (!/^[0-9]{10}$/.test(mobileNumber)) {
      setMobileError('Mobile number must be exactly 10 digits (numbers only)');
      toast.error('Please enter a valid 10-digit mobile number');
      return;
    }
    
    // Double check college roll number format - must be exactly 12 characters
    if (collegeRollNumber.length !== 12) {
      setRollNumberError('College roll number must be exactly 12 characters');
      toast.error('Please enter a valid 12-character college roll number');
      return;
    }
    
    setLoading(true);
    
    try {
      // For registration, we only send name and email along with login credentials
      // The mobile_number and college_roll_number are used for login but aren't updated
      const studentData: StudentRegistrationData = {
        name,
        email,
        mobile_number: mobileNumber,
        college_roll_number: collegeRollNumber,
        college_id: 1, // Default college ID
        dob: dob || undefined // Only include if provided
      };
      
      // Register the student
      const response = await registerStudent(studentData);
      
      // Clear form data
      setName('');
      setEmail('');
      setMobileNumber('');
      setCollegeRollNumber('');
      setDob('');
      
      // Use direct login with the token from registration response
      if (response && response.token && response.user) {
        // Save the student name for the success popup
        setRegisteredName(response.user.name);
        
        // Create user object from response
        const studentUser = {
          id: response.user.id.toString(),
          name: response.user.name,
          mobile: response.user.mobile_number,
          rollNumber: response.user.college_roll_number,
          role: 'student' as const, // Specify literal type
          email: response.user.email || '',
        };
        
        // Save user data and token
        setCurrentUser(studentUser);
        setToken(response.token);
        localStorage.setItem('testAppUser', JSON.stringify(studentUser));
        localStorage.setItem('testAppToken', response.token);
        
        // Show success popup
        setShowSuccessPopup(true);
        
        // Navigate to dashboard after a brief delay
        setTimeout(() => {
          setShowSuccessPopup(false);
          navigate('/student', { replace: true });
        }, 2500);
      } else {
        // Fall back to normal login if response doesn't contain token/user
        const loginSuccess = await login(mobileNumber, collegeRollNumber, 'student');
        
        if (loginSuccess) {
          // Show success popup with form name since we don't have API name
          setRegisteredName(name);
          setShowSuccessPopup(true);
          
          // Navigate to dashboard after a brief delay
          setTimeout(() => {
            setShowSuccessPopup(false);
            navigate('/student', { replace: true });
          }, 2500);
        } else {
          // If auto-login fails, redirect to login page
          toast.error('Auto-login failed. Please try again.');
          setTimeout(() => {
            navigate('/', { replace: true });
          }, 1500);
        }
      }
    } catch (error) {
      let errorMessage = 'Registration failed. Please try again.';
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      toast.error(errorMessage);
      console.error(error);
    } finally {
      setLoading(false);
    }
  };
  
  // Handle input changes with validation
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setEmail(value);
    if (value) validateEmail(value);
    else setEmailError('');
  };
  
  const handleMobileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Only allow digits (0-9) in the mobile number field
    const rawValue = e.target.value;
    const value = rawValue.replace(/\D/g, '').substring(0, 10); // Remove non-digits and limit to 10 chars
    
    // If the value was modified (had non-numeric chars), show a warning
    if (rawValue !== value && rawValue.length > value.length) {
      toast.error('Please enter numbers only for mobile number', { duration: 2000, id: 'mobile-number-digits-only' });
    }
    
    setMobileNumber(value);
    
    // Validate mobile number as user types
    if (value) {
      if (value.length === 10) {
        setMobileError(''); // Clear error when exactly 10 digits
      } else {
        setMobileError(`Mobile number must be exactly 10 digits (${value.length}/10)`);
      }
    } else {
      setMobileError('');
    }
  };
  
  // Restrict input to only allow digits for mobile field
  const handleMobileKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Allow only digit keys, backspace, delete, arrows
    const isDigit = /^\d$/.test(e.key);
    const isControlKey = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key);
    
    if (!isDigit && !isControlKey) {
      e.preventDefault();
      toast.error('Please enter numbers only for mobile number', { duration: 2000, id: 'mobile-number-digits-only' });
    }
  };
  
  const handleRollNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.substring(0, 12); // Limit to maximum 12 characters
    setCollegeRollNumber(value);
    
    // Validate roll number as user types
    if (value) {
      if (value.length === 12) {
        setRollNumberError(''); // Clear error when exactly 12 characters
      } else {
        setRollNumberError(`College roll number must be exactly 12 characters (${value.length}/12)`);
      }
    } else {
      setRollNumberError('');
    }
  };
  
  const handleDobChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setDob(value);
    if (value) validateDob(value);
    else setDobError('');
  };
  
  // Success popup component
  if (showSuccessPopup) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-75">
        <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="mt-4 text-lg font-medium text-gray-900">
              Welcome, {registeredName}!
            </h3>
            <p className="mt-2 text-sm text-gray-500">
              Your registration was successful. Redirecting to your dashboard...
            </p>
            <div className="mt-6">
              <div className="relative">
                <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div className="animate-pulse bg-blue-600 h-full w-full rounded-full"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col justify-center p-4 sm:p-6 lg:p-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <img src={logoImage} alt="Kambaa's Test App Logo" className="h-16 w-auto" />
        </div>
        <h2 className="mt-5 text-center text-3xl font-bold text-gray-900">
          Fill the details to take the Test
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white p-6 sm:p-8 shadow-lg rounded-xl">
          <form className="space-y-5" onSubmit={handleSubmit}>
            {/* Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Full Name
              </label>
              <div className="flex items-center bg-gray-50 rounded-lg border border-gray-300 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 overflow-hidden">
                <div className="pl-4 pr-2">
                  <User className="h-5 w-5 text-gray-400" aria-hidden="true" />
                </div>
                <input
                  id="name"
                  name="name"
                  type="text"
                  required
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="block w-full py-3 bg-transparent border-0 focus:ring-0 text-gray-900"
                  placeholder="Enter your full name"
                />
              </div>
            </div>
            
            {/* Date of Birth */}
            <div>
              <label htmlFor="dob" className="block text-sm font-medium text-gray-700 mb-1">
                Date of Birth
              </label>
              <div className={`flex items-center bg-gray-50 rounded-lg border ${dobError ? 'border-red-500' : 'border-gray-300'} focus-within:ring-2 ${dobError ? 'focus-within:ring-red-500 focus-within:border-red-500' : 'focus-within:ring-blue-500 focus-within:border-blue-500'} overflow-hidden`}>
                <div className="pl-4 pr-2">
                  <Calendar className={`h-5 w-5 ${dobError ? 'text-red-400' : 'text-gray-400'}`} aria-hidden="true" />
                </div>
                <input
                  id="dob"
                  name="dob"
                  type="date"
                  value={dob}
                  onChange={handleDobChange}
                  className={`block w-full py-3 bg-transparent border-0 focus:ring-0 text-gray-900`}
                  max={new Date().toISOString().split('T')[0]} // Set max date to today
                />
              </div>
              {dobError && <p className="mt-1 text-sm text-red-600 font-medium">{dobError}</p>}
            </div>
            
            {/* Email */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                Personal Email Address
              </label>
              <div className={`flex items-center bg-gray-50 rounded-lg border ${emailError ? 'border-red-500' : 'border-gray-300'} focus-within:ring-2 ${emailError ? 'focus-within:ring-red-500 focus-within:border-red-500' : 'focus-within:ring-blue-500 focus-within:border-blue-500'} overflow-hidden`}>
                <div className="pl-4 pr-2">
                  <Mail className={`h-5 w-5 ${emailError ? 'text-red-400' : 'text-gray-400'}`} aria-hidden="true" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={handleEmailChange}
                  className={`block w-full py-3 bg-transparent border-0 focus:ring-0 text-gray-900`}
                  placeholder="Enter your personal email address @gmail.com"
                />
              </div>
              {emailError && <p className="mt-1 text-sm text-red-600 font-medium">{emailError}</p>}
            </div>
            
            {/* Mobile Number */}
            <div>
              <label htmlFor="mobileNumber" className="block text-sm font-medium text-gray-700 mb-1">
                Mobile Number (10 digits)
              </label>
              <div className={`flex items-center bg-gray-50 rounded-lg border ${mobileError ? 'border-red-500' : 'border-gray-300'} focus-within:ring-2 ${mobileError ? 'focus-within:ring-red-500 focus-within:border-red-500' : 'focus-within:ring-blue-500 focus-within:border-blue-500'} overflow-hidden`}>
                <div className="pl-4 pr-2">
                  <Phone className={`h-5 w-5 ${mobileError ? 'text-red-400' : 'text-gray-400'}`} aria-hidden="true" />
                </div>
                <input
                  id="mobileNumber"
                  name="mobileNumber"
                  type="tel"
                  inputMode="numeric"
                  pattern="[0-9]*"
                  autoComplete="tel"
                  required
                  maxLength={10}
                  value={mobileNumber}
                  onChange={handleMobileChange}
                  onKeyDown={handleMobileKeyPress}
                  className={`block w-full py-3 bg-transparent border-0 focus:ring-0 text-gray-900`}
                  placeholder="Enter your 10-digit mobile number"
                />
              </div>
              {mobileError && <p className="mt-1 text-sm text-red-600 font-medium">{mobileError}</p>}
            </div>
            
            {/* College Roll Number */}
            <div>
              <label htmlFor="collegeRollNumber" className="block text-sm font-medium text-gray-700 mb-1">
                College Roll Number (12 characters)
              </label>
              <div className={`flex items-center bg-gray-50 rounded-lg border ${rollNumberError ? 'border-red-500' : 'border-gray-300'} focus-within:ring-2 ${rollNumberError ? 'focus-within:ring-red-500 focus-within:border-red-500' : 'focus-within:ring-blue-500 focus-within:border-blue-500'} overflow-hidden`}>
                <div className="pl-4 pr-2">
                  <Lock className={`h-5 w-5 ${rollNumberError ? 'text-red-400' : 'text-gray-400'}`} aria-hidden="true" />
                </div>
                <input
                  id="collegeRollNumber"
                  name="collegeRollNumber"
                  type="text"
                  required
                  maxLength={12}
                  value={collegeRollNumber}
                  onChange={handleRollNumberChange}
                  className={`block w-full py-3 bg-transparent border-0 focus:ring-0 text-gray-900`}
                  placeholder="Enter your 12-character college roll number"
                />
              </div>
              {rollNumberError && <p className="mt-1 text-sm text-red-600 font-medium">{rollNumberError}</p>}
            </div>

            <div className="pt-2">
              <button
                type="submit"
                disabled={loading || !!emailError || !!mobileError || !!rollNumberError || !!dobError || !name || !email || !mobileNumber || !collegeRollNumber || mobileNumber.length !== 10 || collegeRollNumber.length !== 12}
                className={`w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white
                  ${loading || !!emailError || !!mobileError || !!rollNumberError || !!dobError || !name || !email || !mobileNumber || !collegeRollNumber || mobileNumber.length !== 10 || collegeRollNumber.length !== 12
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'} 
                  transition-colors`}
                title={
                  !name ? 'Please enter your name' :
                  !!emailError ? emailError :
                  !email ? 'Please enter your email' :
                  !!mobileError ? mobileError :
                  mobileNumber.length !== 10 ? 'Mobile number must be exactly 10 digits' :
                  !!rollNumberError ? rollNumberError :
                  collegeRollNumber.length !== 12 ? 'College roll number must be exactly 12 characters' :
                  !!dobError ? dobError :
                  loading ? 'Registering...' : 'Register Account'
                }
              >
                {loading ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Registering...
                  </span>
                ) : (
                  <span className="flex items-center">
                    <UserPlus className="mr-2 h-4 w-4" />
                    Register Account
                  </span>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage; 