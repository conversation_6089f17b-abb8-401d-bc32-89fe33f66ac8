import { useState, useEffect } from 'react';
import { Check<PERSON>ir<PERSON>, Circle } from 'lucide-react';
import { Question } from '../types/test';

interface QuestionCardProps {
  question: Question;
  questionNumber: number;
  selectedAnswer: string | undefined;
  onAnswerSelect: (answer: string) => void;
}

const QuestionCard = ({ 
  question, 
  questionNumber, 
  selectedAnswer, 
  onAnswerSelect 
}: QuestionCardProps) => {
  const [selectedOption, setSelectedOption] = useState<string | undefined>(selectedAnswer);
  const [hasAnimated, setHasAnimated] = useState(false);
  
  useEffect(() => {
    setSelectedOption(selectedAnswer);
  }, [selectedAnswer]);
  
  useEffect(() => {
    // Trigger animation when question changes
    setHasAnimated(false);
    const timer = setTimeout(() => setHasAnimated(true), 50);
    return () => clearTimeout(timer);
  }, [question.id]);
  
  const handleOptionSelect = (option: string) => {
    setSelectedOption(option);
    onAnswerSelect(option);
  };

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${hasAnimated ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'} transition-all duration-500`}>
      <div className="flex items-center justify-between mb-4">
        <span className="text-sm font-medium text-gray-500">
          Question {questionNumber}
        </span>
        <span className="text-xs font-medium px-2 py-1 rounded-full bg-blue-100 text-blue-800">
          {question.category}
        </span>
      </div>
      
      <h3 className="text-lg font-medium text-gray-900 mb-6">
        {question.text}
      </h3>
      
      <div className="space-y-3">
        {question.options.map((option, index) => {
          const isSelected = selectedOption === option;
          const optionLetter = String.fromCharCode(65 + index); // A, B, C, D
          
          return (
            <button
              key={index}
              onClick={() => handleOptionSelect(option)}
              className={`flex items-center w-full text-left p-3 rounded-lg border transition-all ${
                isSelected 
                  ? 'border-blue-500 bg-blue-50 text-blue-800' 
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }`}
            >
              {isSelected ? (
                <CheckCircle size={20} className="text-blue-500 mr-3 flex-shrink-0" />
              ) : (
                <Circle size={20} className="text-gray-300 mr-3 flex-shrink-0" />
              )}
              <div>
                <span className="font-medium mr-2">{optionLetter}.</span>
                {option}
              </div>
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default QuestionCard;