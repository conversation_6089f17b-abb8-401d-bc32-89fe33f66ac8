import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

const ResultPage = () => {
  const navigate = useNavigate();
  
  useEffect(() => {
    // We no longer show this page after test completion
    // Redirect to student dashboard in all cases
    navigate('/student');
  }, [navigate]);
  
  // The rest of the component will never render because of the immediate redirect
  return null;
};

export default ResultPage;