import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  Plus, 
  Search, 
  Trash2, 
  Edit, 
  Save, 
  X,
  Download,
  Upload,
  Filter
} from 'lucide-react';
import toast from 'react-hot-toast';
import { Question } from '../../types/test';
import { useAuth } from '../../contexts/AuthContext';
import {
  getQuestions,
  addQuestion,
  updateQuestion,
  bulkAddQuestions,
  QuestionAPIData
} from '../../utils/api';

// Transform API question to UI question
const transformAPIToUI = (apiQ: any): Question => ({
  id: apiQ.id?.toString() || '',
  text: apiQ.question_text,
  options: [apiQ.option_a, apiQ.option_b, apiQ.option_c, apiQ.option_d],
  correctAnswer: apiQ.correct_answer,
  category: apiQ.question_type,
});

// Transform UI question to API question
const transformUIToAPI = (q: Question): QuestionAPIData => ({
  question_text: q.text,
  option_a: q.options[0],
  option_b: q.options[1],
  option_c: q.options[2],
  option_d: q.options[3],
  correct_answer: q.correctAnswer,
  question_type: q.category,
});

// Modal component
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}
const Modal = ({ isOpen, onClose, title, children }: ModalProps) => {
  if (!isOpen) return null;
  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onClick={onClose}></div>
        <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
        <div className="inline-block overflow-hidden text-left align-bottom transition-all transform bg-white rounded-lg shadow-xl sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="px-4 pt-5 pb-4 bg-white sm:p-6 sm:pb-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium leading-6 text-gray-900">{title}</h3>
              <button onClick={onClose} className="text-gray-400 hover:text-gray-500 focus:outline-none">
                <X className="h-5 w-5" />
              </button>
            </div>
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

const QuestionManagement = () => {
  const navigate = useNavigate();
  const { token } = useAuth();
  
  const [questions, setQuestions] = useState<Question[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editFormData, setEditFormData] = useState<Question | null>(null);
  
  // New question form
  const [newQuestion, setNewQuestion] = useState<Question>({
    id: '',
    text: '',
    options: ['', '', '', ''],
    correctAnswer: '',
    category: 'logical_reasoning',
  });
  
  // Fetch questions from API on mount
  useEffect(() => {
    const fetchQuestions = async () => {
      if (!token) return;
      try {
        const apiQuestions = await getQuestions(token);
        // API may return { questions: [...] } or just an array
        const list = Array.isArray(apiQuestions) ? apiQuestions : apiQuestions.questions || [];
        setQuestions(list.map(transformAPIToUI));
      } catch (error: any) {
        toast.error(error.message || 'Failed to load questions');
      }
    };
    fetchQuestions();
  }, [token]);
  
  // Add Question (API)
  const handleAddQuestion = async () => {
    if (!newQuestion.text || newQuestion.options.some(opt => !opt) || !newQuestion.correctAnswer) {
      toast.error('Please fill in all required fields');
      return;
    }
    if (!newQuestion.options.includes(newQuestion.correctAnswer)) {
      toast.error('The correct answer must be one of the options');
      return;
    }
    try {
      if (!token) throw new Error('No token');
      await addQuestion(transformUIToAPI(newQuestion), token);
      toast.success('Question added successfully');
      setShowAddModal(false);
      setNewQuestion({ id: '', text: '', options: ['', '', '', ''], correctAnswer: '', category: 'logical_reasoning' });
      // Refresh
      const apiQuestions = await getQuestions(token);
      const list = Array.isArray(apiQuestions) ? apiQuestions : apiQuestions.questions || [];
      setQuestions(list.map(transformAPIToUI));
    } catch (error: any) {
      toast.error(error.message || 'Failed to add question');
    }
  };
  
  // Edit Question (API)
  const startEditing = (question: Question) => {
    setEditFormData({ ...question });
    setShowEditModal(true);
  };
  const cancelEditing = () => {
    setShowEditModal(false);
    setEditFormData(null);
  };
  const submitEdit = async () => {
    if (!editFormData) return;
    try {
      if (!token) throw new Error('No token');
      await updateQuestion(editFormData.id, transformUIToAPI(editFormData), token);
      toast.success('Question updated successfully');
      setShowEditModal(false);
      setEditFormData(null);
      // Refresh
      const apiQuestions = await getQuestions(token);
      const list = Array.isArray(apiQuestions) ? apiQuestions : apiQuestions.questions || [];
      setQuestions(list.map(transformAPIToUI));
    } catch (error: any) {
      toast.error(error.message || 'Failed to update question');
    }
  };
  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    if (!editFormData) return;
    const { name, value } = e.target;
    if (name.startsWith('option')) {
      const idx = parseInt(name.replace('option', ''));
      setEditFormData({ ...editFormData, options: editFormData.options.map((opt, i) => i === idx ? value : opt) });
    } else {
      setEditFormData({ ...editFormData, [name]: value });
    }
  };
  
  const handleDeleteQuestion = (id: string) => {
    if (window.confirm('Are you sure you want to delete this question?')) {
      setQuestions(questions.filter(question => question.id !== id));
      toast.success('Question deleted successfully');
    }
  };
  
  const handleOptionChange = (index: number, value: string) => {
    const newOptions = [...newQuestion.options];
    newOptions[index] = value;
    setNewQuestion({
      ...newQuestion,
      options: newOptions
    });
  };
  
  const filteredQuestions = questions.filter(question => {
    const matchesSearch = question.text.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || question.category === categoryFilter;
    return matchesSearch && matchesCategory;
  });
  
  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: 'logical_reasoning', label: 'Logical Reasoning' },
    { value: 'common_sense', label: 'Common Sense' }
  ];
  
  const handleExportCSV = () => {
    // Create CSV content
    const csvContent = [
      ['Text', 'Option A', 'Option B', 'Option C', 'Option D', 'Correct Answer', 'Category'],
      ...questions.map(question => [
        question.text,
        ...question.options,
        question.correctAnswer,
        question.category
      ])
    ].map(row => row.join(',')).join('\n');
    
    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.setAttribute('hidden', '');
    a.setAttribute('href', url);
    a.setAttribute('download', 'questions.csv');
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    toast.success('Questions data exported successfully');
  };
  
  const handleImportCSV = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = (event) => {
      const csvText = event.target?.result as string;
      const rows = csvText.split('\n');
      
      // Skip header row
      const newQuestions: Question[] = [];
      let hasErrors = false;
      
      for (let i = 1; i < rows.length; i++) {
        if (!rows[i].trim()) continue;
        
        const cols = rows[i].split(',');
        if (cols.length < 6) {
          hasErrors = true;
          continue;
        }
        
        const question: Question = {
          id: `question-${Date.now()}-${i}`,
          text: cols[0].trim(),
          options: [
            cols[1].trim(),
            cols[2].trim(),
            cols[3].trim(),
            cols[4].trim()
          ],
          correctAnswer: cols[5].trim(),
          category: cols[6]?.trim() === 'common_sense' ? 'common_sense' : 'logical_reasoning'
        };
        
        if (!question.text || question.options.some(opt => !opt) || !question.correctAnswer) {
          hasErrors = true;
          continue;
        }
        
        newQuestions.push(question);
      }
      
      if (newQuestions.length > 0) {
        setQuestions(prev => [...prev, ...newQuestions]);
        toast.success(`Imported ${newQuestions.length} questions successfully`);
      }
      
      if (hasErrors) {
        toast.error('Some rows had errors and were skipped');
      }
    };
    
    reader.readAsText(file);
  };
  
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex items-center">
          <button
            onClick={() => navigate('/admin')}
            className="mr-4 text-gray-400 hover:text-gray-500"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <h1 className="text-xl font-semibold text-gray-900">Question Management</h1>
        </div>
      </div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
            <div className="flex flex-col sm:flex-row gap-4 w-full md:w-auto">
              <div className="relative rounded-md w-full sm:w-64">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="Search questions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="relative rounded-md w-full sm:w-48">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Filter className="h-5 w-5 text-gray-400" />
                </div>
                <select
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value)}
                >
                  {categories.map((category) => (
                    <option key={category.value} value={category.value}>
                      {category.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div className="flex space-x-2 w-full md:w-auto">
              <button
                onClick={() => setShowAddModal(true)}
                className="flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Question
              </button>
              <button
                onClick={handleExportCSV}
                className="flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Download className="h-4 w-4 mr-1" />
                Export CSV
              </button>
              <label className="flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer">
                <Upload className="h-4 w-4 mr-1" />
                Import CSV
                <input
                  type="file"
                  accept=".csv"
                  className="hidden"
                  onChange={handleImportCSV}
                />
              </label>
            </div>
          </div>
          
          {showAddModal && (
            <Modal isOpen={showAddModal} onClose={() => setShowAddModal(false)} title="Add New Question">
              <div className="bg-gray-50 rounded-lg p-6 shadow-inner space-y-6">
                <div>
                  <label htmlFor="category" className="block text-sm font-semibold text-gray-700 mb-2">
                    Category *
                  </label>
                  <select
                    id="category"
                    name="category"
                    value={newQuestion.category}
                    onChange={e => setNewQuestion({ ...newQuestion, category: e.target.value })}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-lg shadow-sm bg-white"
                  >
                    <option value="logical_reasoning">Logical Reasoning</option>
                    <option value="common_sense">Common Sense</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="text" className="block text-sm font-semibold text-gray-700 mb-2">
                    Question Text *
                  </label>
                  <textarea
                    id="text"
                    name="text"
                    rows={3}
                    value={newQuestion.text}
                    onChange={e => setNewQuestion({ ...newQuestion, text: e.target.value })}
                    className="mt-1 block w-full shadow-sm sm:text-sm focus:ring-blue-500 focus:border-blue-500 border border-gray-300 rounded-lg bg-white"
                    placeholder="Enter the question text here..."
                    required
                  />
                </div>
                <div className="space-y-3">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Options *
                  </label>
                  <div className="grid grid-cols-1 gap-3">
                    {newQuestion.options.map((option, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <span className="text-sm font-medium w-6">{String.fromCharCode(65 + index)}.</span>
                        <input
                          type="text"
                          value={option}
                          onChange={e => handleOptionChange(index, e.target.value)}
                          className="block w-full shadow-sm sm:text-sm focus:ring-blue-500 focus:border-blue-500 border border-gray-300 rounded-lg bg-white"
                          placeholder={`Option ${String.fromCharCode(65 + index)}`}
                          required
                        />
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <label htmlFor="correctAnswer" className="block text-sm font-semibold text-gray-700 mb-2">
                    Correct Answer *
                  </label>
                  <select
                    id="correctAnswer"
                    name="correctAnswer"
                    value={newQuestion.correctAnswer}
                    onChange={e => setNewQuestion({ ...newQuestion, correctAnswer: e.target.value })}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-lg shadow-sm bg-white"
                  >
                    <option value="">Select correct answer</option>
                    {newQuestion.options.map((option, index) => (
                      option && (
                        <option key={index} value={option}>
                          {String.fromCharCode(65 + index)}. {option}
                        </option>
                      )
                    ))}
                  </select>
                </div>
              </div>
              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={() => setShowAddModal(false)}
                  className="px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  onClick={handleAddQuestion}
                  className="px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Add Question
                </button>
              </div>
            </Modal>
          )}
          
          {showEditModal && editFormData && (
            <Modal isOpen={showEditModal} onClose={cancelEditing} title="Edit Question">
              <div className="bg-gray-50 rounded-lg p-6 shadow-inner space-y-6">
                <div>
                  <label htmlFor="category" className="block text-sm font-semibold text-gray-700 mb-2">
                    Category *
                  </label>
                  <select
                    id="category"
                    name="category"
                    value={editFormData.category}
                    onChange={handleEditInputChange}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-lg shadow-sm bg-white"
                  >
                    <option value="logical_reasoning">Logical Reasoning</option>
                    <option value="common_sense">Common Sense</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="text" className="block text-sm font-semibold text-gray-700 mb-2">
                    Question Text *
                  </label>
                  <textarea
                    id="text"
                    name="text"
                    rows={3}
                    value={editFormData.text}
                    onChange={handleEditInputChange}
                    className="mt-1 block w-full shadow-sm sm:text-sm focus:ring-blue-500 focus:border-blue-500 border border-gray-300 rounded-lg bg-white"
                    placeholder="Enter the question text here..."
                    required
                  />
                </div>
                <div className="space-y-3">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Options *
                  </label>
                  <div className="grid grid-cols-1 gap-3">
                    {editFormData.options.map((option, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <span className="text-sm font-medium w-6">{String.fromCharCode(65 + index)}.</span>
                        <input
                          type="text"
                          name={`option${index}`}
                          value={option}
                          onChange={handleEditInputChange}
                          className="block w-full shadow-sm sm:text-sm focus:ring-blue-500 focus:border-blue-500 border border-gray-300 rounded-lg bg-white"
                          placeholder={`Option ${String.fromCharCode(65 + index)}`}
                          required
                        />
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <label htmlFor="correctAnswer" className="block text-sm font-semibold text-gray-700 mb-2">
                    Correct Answer *
                  </label>
                  <select
                    id="correctAnswer"
                    name="correctAnswer"
                    value={editFormData.correctAnswer}
                    onChange={handleEditInputChange}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-lg shadow-sm bg-white"
                  >
                    <option value="">Select correct answer</option>
                    {editFormData.options.map((option, index) => (
                      option && (
                        <option key={index} value={option}>
                          {String.fromCharCode(65 + index)}. {option}
                        </option>
                      )
                    ))}
                  </select>
                </div>
              </div>
              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={cancelEditing}
                  className="px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  onClick={submitEdit}
                  className="px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Update Question
                </button>
              </div>
            </Modal>
          )}
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Question
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Category
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Options
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Correct Answer
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredQuestions.length > 0 ? (
                  filteredQuestions.map((question) => (
                    <tr key={question.id}>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900">{question.text}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          question.category === 'logical_reasoning' 
                            ? 'bg-blue-100 text-blue-800' 
                            : 'bg-purple-100 text-purple-800'
                        }`}>
                          {question.category === 'logical_reasoning' ? 'Logical Reasoning' : 'Common Sense'}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-500">
                          {question.options.map((option, index) => (
                            <div key={index}>
                              {String.fromCharCode(65 + index)}. {option}
                            </div>
                          ))}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-green-600 font-medium">{question.correctAnswer}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => startEditing(question)}
                          className="text-indigo-600 hover:text-indigo-900 mr-3"
                        >
                          <Edit className="h-5 w-5" />
                        </button>
                        <button
                          onClick={() => handleDeleteQuestion(question.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 className="h-5 w-5" />
                        </button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                      No questions found. {searchTerm || categoryFilter !== 'all' ? 'Try different search criteria.' : 'Add a question to get started.'}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          
          <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 text-sm text-gray-500">
            Showing {filteredQuestions.length} of {questions.length} questions
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuestionManagement;