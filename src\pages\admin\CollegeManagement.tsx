import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  Plus, 
  Search, 
  Trash2, 
  Edit, 
  Save, 
  X,
  Mail,
  Phone,
  MapPin,
  Users,
  Building,
  Loader,
  Check
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { 
  CollegeData, 
  getAllColleges, 
  createCollege, 
  updateCollege, 
  deleteCollege, 
  getCollegeUsers 
} from '../../utils/api';
import toast from 'react-hot-toast';
import logoImage from '../../assets/images/logo.png';

const CollegeManagement = () => {
  const { token } = useAuth();
  const navigate = useNavigate();
  
  const [colleges, setColleges] = useState<CollegeData[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  
  const [isAddMode, setIsAddMode] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedCollege, setSelectedCollege] = useState<CollegeData | null>(null);
  const [collegeStudents, setCollegeStudents] = useState<any[]>([]);
  const [showStudents, setShowStudents] = useState(false);
  
  const [formData, setFormData] = useState<CollegeData>({
    name: '',
    address: '',
    city: '',
    state: '',
    contact_email: '',
    contact_phone: ''
  });
  
  // Load colleges on component mount
  useEffect(() => {
    fetchColleges();
  }, [token]);
  
  const fetchColleges = async () => {
    try {
      setLoading(true);
      if (!token) return;
      
      const response = await getAllColleges(token);
      setColleges(response.colleges || []);
    } catch (error) {
      console.error('Error fetching colleges:', error);
      toast.error('Failed to load colleges');
    } finally {
      setLoading(false);
    }
  };
  
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };
  
  const filteredColleges = colleges.filter(college => 
    college.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    college.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
    college.state.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  const handleAddNewClick = () => {
    setFormData({
      name: '',
      address: '',
      city: '',
      state: '',
      contact_email: '',
      contact_phone: ''
    });
    setIsAddMode(true);
    setIsEditMode(false);
    setSelectedCollege(null);
    setShowStudents(false);
  };
  
  const handleEditClick = (college: CollegeData) => {
    setFormData({
      name: college.name,
      address: college.address,
      city: college.city,
      state: college.state,
      contact_email: college.contact_email,
      contact_phone: college.contact_phone
    });
    setSelectedCollege(college);
    setIsEditMode(true);
    setIsAddMode(false);
    setShowStudents(false);
  };
  
  const handleDeleteClick = async (collegeId: number | undefined) => {
    if (!collegeId) return;
    
    if (window.confirm('Are you sure you want to delete this college? This action cannot be undone.')) {
      try {
        await deleteCollege(collegeId.toString(), token!);
        toast.success('College deleted successfully');
        fetchColleges();
      } catch (error) {
        console.error('Error deleting college:', error);
        toast.error('Failed to delete college');
      }
    }
  };
  
  const handleViewStudents = async (collegeId: number | undefined) => {
    if (!collegeId) return;
    
    try {
      setLoading(true);
      const response = await getCollegeUsers(collegeId.toString(), token!);
      setCollegeStudents(response.users || []);
      setSelectedCollege(colleges.find(c => c.id === collegeId) || null);
      setShowStudents(true);
      setIsAddMode(false);
      setIsEditMode(false);
    } catch (error) {
      console.error('Error fetching college students:', error);
      toast.error('Failed to load college students');
    } finally {
      setLoading(false);
    }
  };
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (isAddMode) {
        await createCollege(formData, token!);
        toast.success('College added successfully');
      } else if (isEditMode && selectedCollege) {
        await updateCollege(selectedCollege.id!.toString(), formData, token!);
        toast.success('College updated successfully');
      }
      
      setIsAddMode(false);
      setIsEditMode(false);
      fetchColleges();
    } catch (error) {
      console.error('Error saving college:', error);
      toast.error('Failed to save college');
    }
  };
  
  const handleCancel = () => {
    setIsAddMode(false);
    setIsEditMode(false);
    setSelectedCollege(null);
  };
  
  const handleBackClick = () => {
    if (showStudents) {
      setShowStudents(false);
      setSelectedCollege(null);
    } else {
      navigate('/admin');
    }
  };
  
  if (loading && colleges.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex flex-col items-center">
          <Loader className="h-8 w-8 text-blue-600 animate-spin" />
          <p className="mt-4 text-gray-600">Loading college data...</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
          <div className="flex items-center">
            <img src={logoImage} alt="Kambaa's Test App Logo" className="h-10 w-auto mr-2" />
            <span className="text-xl font-semibold text-gray-900">College Management</span>
          </div>
          <button
            onClick={handleBackClick}
            className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            {showStudents ? 'Back to Colleges' : 'Back to Dashboard'}
          </button>
        </div>
      </div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* College List View */}
        {!isAddMode && !isEditMode && !showStudents && (
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-5 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900 flex items-center">
                <Building className="h-5 w-5 text-gray-500 mr-2" />
                Colleges
              </h2>
              <div className="flex space-x-3">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    className="block w-72 pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="Search colleges..."
                    value={searchTerm}
                    onChange={handleSearch}
                  />
                </div>
                <button
                  onClick={handleAddNewClick}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add New College
                </button>
              </div>
            </div>
            
            <div className="overflow-x-auto bg-white">
              {filteredColleges.length > 0 ? (
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        College Name
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Location
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Contact Info
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredColleges.map((college) => (
                      <tr key={college.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="ml-0">
                              <div className="text-sm font-medium text-gray-900">{college.name}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{college.city}</div>
                          <div className="text-sm text-gray-500">{college.state}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 flex items-center">
                            <Mail className="h-4 w-4 text-gray-400 mr-1" />
                            {college.contact_email}
                          </div>
                          <div className="text-sm text-gray-500 flex items-center">
                            <Phone className="h-4 w-4 text-gray-400 mr-1" />
                            {college.contact_phone}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex justify-end space-x-2">
                            <button
                              onClick={() => handleViewStudents(college.id)}
                              className="text-indigo-600 hover:text-indigo-900 bg-indigo-50 p-1.5 rounded"
                              title="View Students"
                            >
                              <Users className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleEditClick(college)}
                              className="text-blue-600 hover:text-blue-900 bg-blue-50 p-1.5 rounded"
                              title="Edit"
                            >
                              <Edit className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleDeleteClick(college.id)}
                              className="text-red-600 hover:text-red-900 bg-red-50 p-1.5 rounded"
                              title="Delete"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              ) : (
                <div className="text-center py-10">
                  <Building className="mx-auto h-12 w-12 text-gray-300" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No colleges found</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {searchTerm ? 'No colleges match your search criteria' : 'Get started by adding a new college'}
                  </p>
                  <div className="mt-6">
                    <button
                      onClick={handleAddNewClick}
                      className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add New College
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
        
        {/* College Form (Add/Edit Mode) */}
        {(isAddMode || isEditMode) && (
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-5 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">
                {isAddMode ? 'Add New College' : 'Edit College'}
              </h2>
            </div>
            
            <form onSubmit={handleFormSubmit} className="px-6 py-5">
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                    College Name
                  </label>
                  <input
                    type="text"
                    name="name"
                    id="name"
                    required
                    value={formData.name}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                
                <div>
                  <label htmlFor="address" className="block text-sm font-medium text-gray-700">
                    Address
                  </label>
                  <input
                    type="text"
                    name="address"
                    id="address"
                    required
                    value={formData.address}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                
                <div>
                  <label htmlFor="city" className="block text-sm font-medium text-gray-700">
                    City
                  </label>
                  <input
                    type="text"
                    name="city"
                    id="city"
                    required
                    value={formData.city}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                
                <div>
                  <label htmlFor="state" className="block text-sm font-medium text-gray-700">
                    State
                  </label>
                  <input
                    type="text"
                    name="state"
                    id="state"
                    required
                    value={formData.state}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                
                <div>
                  <label htmlFor="contact_email" className="block text-sm font-medium text-gray-700">
                    Contact Email
                  </label>
                  <input
                    type="email"
                    name="contact_email"
                    id="contact_email"
                    required
                    value={formData.contact_email}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                
                <div>
                  <label htmlFor="contact_phone" className="block text-sm font-medium text-gray-700">
                    Contact Phone
                  </label>
                  <input
                    type="tel"
                    name="contact_phone"
                    id="contact_phone"
                    required
                    value={formData.contact_phone}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>
              
              <div className="mt-6 flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={handleCancel}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  <X className="h-4 w-4 mr-1" />
                  Cancel
                </button>
                <button
                  type="submit"
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  <Save className="h-4 w-4 mr-1" />
                  {isAddMode ? 'Create College' : 'Update College'}
                </button>
              </div>
            </form>
          </div>
        )}
        
        {/* College Students View */}
        {showStudents && selectedCollege && (
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-5 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900 flex items-center">
                <Users className="h-5 w-5 text-gray-500 mr-2" />
                Students of {selectedCollege.name}
              </h2>
            </div>
            
            <div className="overflow-x-auto bg-white">
              {collegeStudents.length > 0 ? (
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Student Name
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Roll Number
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Email
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Mobile
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {collegeStudents.map((student, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{student.name}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{student.college_roll_number}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{student.email}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{student.mobile_number}</div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              ) : (
                <div className="text-center py-10">
                  <Users className="mx-auto h-12 w-12 text-gray-300" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No students found</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    This college doesn't have any students yet
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CollegeManagement; 