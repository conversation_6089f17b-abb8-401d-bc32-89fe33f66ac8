// API base URL
export const API_BASE_URL = 'http://localhost:3000/api';
// export const API_BASE_URL = 'https://aptitude.kambaaincorporation.in/api';

// Auth endpoints
export const AUTH_ENDPOINTS = {
  LOGIN_STUDENT: `${API_BASE_URL}/auth/student/login`,
  LOGIN_ADMIN: `${API_BASE_URL}/auth/login/admin`,
  REGISTER_STUDENT: `${API_BASE_URL}/auth/student/auth`,
};

// Student management endpoints
export const STUDENT_ENDPOINTS = {
  GET_STUDENTS: `${API_BASE_URL}/users/students`,
  ADD_STUDENT: `${API_BASE_URL}/auth/student/auth`,
  UPDATE_STUDENT: (id: string) => `${API_BASE_URL}/admin/users/${id}`,
  DELETE_STUDENT: (id: string) => `${API_BASE_URL}/users/${id}`,
};

// Question management endpoints
export const QUESTION_ENDPOINTS = {
  GET_QUESTIONS: `${API_BASE_URL}/questions`,
  ADD_QUESTION: `${API_BASE_URL}/questions`,
  UPDATE_QUESTION: (id: string) => `${API_BASE_URL}/questions/${id}`,
  BULK_ADD: `${API_BASE_URL}/questions/bulk`,
};

// Test management endpoints
export const TEST_ENDPOINTS = {
  START_TEST: `${API_BASE_URL}/tests/start`,
  SUBMIT_TEST: `${API_BASE_URL}/tests/complete`,
  GET_TEST_RESULT: (id: string) => `${API_BASE_URL}/tests/results/${id}`,
  GET_ALL_TESTS: `${API_BASE_URL}/admin/tests`,
  PROCTORING_EVENT: `${API_BASE_URL}/tests/proctoring-event`,
};

// College management endpoints
export const COLLEGE_ENDPOINTS = {
  GET_ALL_COLLEGES: `${API_BASE_URL}/colleges`,
  GET_COLLEGE: (id: string) => `${API_BASE_URL}/colleges/${id}`,
  GET_COLLEGE_USERS: (id: string) => `${API_BASE_URL}/colleges/${id}/users`,
  CREATE_COLLEGE: `${API_BASE_URL}/colleges`,
  UPDATE_COLLEGE: (id: string) => `${API_BASE_URL}/colleges/${id}`,
  DELETE_COLLEGE: (id: string) => `${API_BASE_URL}/colleges/${id}`,
};

// Helper function for making API requests
export const apiRequest = async (
  url: string, 
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
  data?: any,
  headers: HeadersInit = {}
) => {
  const options: RequestInit = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  const response = await fetch(url, options);
  
  if (!response.ok) {
    // Handle error responses
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || 'Something went wrong');
  }

  return response.json();
};

// Auth interfaces
export interface StudentRegistrationData {
  name: string;
  email: string;
  mobile_number: string;
  college_roll_number: string;
  college_id: number;
  dob?: string;
  password?: string;
}

// Student management interfaces
export interface StudentData {
  name: string;
  email: string;
  mobile_number: string;
  college_roll_number: string;
  college_id: number;
  dob?: string;
  password?: string;
}

// Question management interfaces
export interface QuestionAPIData {
  question_text: string;
  option_a: string;
  option_b: string;
  option_c: string;
  option_d: string;
  correct_answer: string; // 'A', 'B', 'C', or 'D'
  question_type: string; // 'common_sense' or 'logical_reasoning'
}

// Test management interfaces
export interface TestQuestion {
  id: number;
  question_text: string;
  option_a: string;
  option_b: string;
  option_c: string;
  option_d: string;
  correct_answer: string;
  question_type: string;
  created_at: string;
  updated_at: string;
}

export interface AnswerSubmission {
  question_id: string;
  selected_option: string;
}

export interface StartTestResponse {
  message: string;
  test: {
    id: number;
    user_id: number;
    start_time: string;
    end_time: null | string;
    is_completed: number;
    score: number;
    created_at: string;
    updated_at: string;
  };
  questions: TestQuestion[];
}

export interface SubmitTestResponse {
  message: string;
  test: {
    testId: number;
    userId: number;
    studentName: string;
    mobileNumber: string;
    collegeRollNumber: string;
    startTime: string;
    endTime: string;
    isCompleted: boolean;
    duration: number;
    score: number;
    stats: {
      total_questions: number;
      correct_answers: number | null;
      wrong_answers: number | null;
      logical_reasoning_total: number;
      logical_reasoning_correct: number | null;
      common_sense_total: number;
      common_sense_correct: number | null;
    }
  };
}

export interface TestResult {
  id: number;
  user_id: number;
  start_time: string;
  end_time: string | null;
  is_completed: number;
  score: number;
  created_at: string;
  updated_at: string;
  name: string;
  mobile_number: string;
  college_roll_number: string;
}

export interface TestResultsResponse {
  tests: TestResult[];
}

export interface TestDetailStats {
  totalQuestions: number;
  attemptedQuestions: number;
  notAttemptedQuestions: number;
  correctAnswers: string;
  wrongAnswers: string;
  correctPercentage: number;
  wrongPercentage: number;
  notAttemptedPercentage: number;
  logical_reasoning_total: number;
  logical_reasoning_correct: string;
  common_sense_total: number;
  common_sense_correct: string;
}

export interface TestDetailResponse {
  test: {
    testId: number;
    userId: number;
    studentName: string;
    mobileNumber: string;
    collegeRollNumber: string;
    email: string;
    startTime: string;
    endTime: string;
    isCompleted: boolean;
    duration: number;
    score: number;
    stats: TestDetailStats;
  }
}

// College management interfaces
export interface CollegeData {
  id?: number;
  name: string;
  address: string;
  city: string;
  state: string;
  contact_email: string;
  contact_phone: string;
  created_at?: string;
  updated_at?: string;
}

export interface CollegeUsersResponse {
  users: StudentData[];
}

export interface CollegesResponse {
  colleges: CollegeData[];
}

// Registration response interface
export interface StudentRegistrationResponse {
  message: string;
  token: string;
  user: {
    id: number;
    name: string;
    email: string;
    mobile_number: string;
    college_roll_number: string;
    college_id: number;
    created_at: string;
    updated_at: string;
  }
}

// Auth functions
export const registerStudent = async (studentData: StudentRegistrationData): Promise<StudentRegistrationResponse> => {
  return apiRequest(AUTH_ENDPOINTS.REGISTER_STUDENT, 'POST', studentData);
};

// Student management functions
export const getStudents = async (token: string) => {
  return apiRequest(
    STUDENT_ENDPOINTS.GET_STUDENTS, 
    'GET', 
    undefined, 
    { Authorization: `Bearer ${token}` }
  );
};

export const addStudent = async (studentData: StudentData, token: string) => {
  return apiRequest(
    STUDENT_ENDPOINTS.ADD_STUDENT, 
    'POST', 
    studentData, 
    { Authorization: `Bearer ${token}` }
  );
};

export const updateStudent = async (id: string, studentData: Partial<StudentData>, token: string) => {
  return apiRequest(
    STUDENT_ENDPOINTS.UPDATE_STUDENT(id), 
    'PUT', 
    studentData, 
    { Authorization: `Bearer ${token}` }
  );
};

export const deleteStudent = async (id: string, token: string) => {
  return apiRequest(
    STUDENT_ENDPOINTS.DELETE_STUDENT(id), 
    'DELETE', 
    undefined, 
    { Authorization: `Bearer ${token}` }
  );
};

// Fetch all questions
export const getQuestions = async (token: string) => {
  return apiRequest(
    QUESTION_ENDPOINTS.GET_QUESTIONS,
    'GET',
    undefined,
    { Authorization: `Bearer ${token}` }
  );
};

// Add a single question
export const addQuestion = async (questionData: QuestionAPIData, token: string) => {
  return apiRequest(
    QUESTION_ENDPOINTS.ADD_QUESTION,
    'POST',
    questionData,
    { Authorization: `Bearer ${token}` }
  );
};

// Update a question
export const updateQuestion = async (id: string, questionData: Partial<QuestionAPIData>, token: string) => {
  return apiRequest(
    QUESTION_ENDPOINTS.UPDATE_QUESTION(id),
    'PUT',
    questionData,
    { Authorization: `Bearer ${token}` }
  );
};

// Bulk add questions
export const bulkAddQuestions = async (questions: QuestionAPIData[], token: string) => {
  return apiRequest(
    QUESTION_ENDPOINTS.BULK_ADD,
    'POST',
    { questions },
    { Authorization: `Bearer ${token}` }
  );
};

// Test management functions
export const startTest = async (token: string): Promise<StartTestResponse> => {
  return apiRequest(
    TEST_ENDPOINTS.START_TEST, 
    'POST', 
    {},
    { Authorization: `Bearer ${token}` }
  );
};

export const submitTest = async (
  testId: number,
  answers: AnswerSubmission[],
  token: string
): Promise<SubmitTestResponse> => {
  return apiRequest(
    TEST_ENDPOINTS.SUBMIT_TEST,
    'POST',
    { 
      test_id: testId.toString(),
      answers
    },
    { Authorization: `Bearer ${token}` }
  );
};

export const getTestResult = async (
  testId: string,
  token: string
): Promise<TestResultsResponse> => {
  return apiRequest(
    TEST_ENDPOINTS.GET_TEST_RESULT(testId),
    'GET',
    undefined,
    { Authorization: `Bearer ${token}` }
  );
};

export const getAllTests = async (
  token: string
): Promise<TestResultsResponse> => {
  return apiRequest(
    TEST_ENDPOINTS.GET_ALL_TESTS,
    'GET',
    undefined,
    { Authorization: `Bearer ${token}` }
  );
};

// Get a specific test result with details
export const getTestDetail = async (
  testId: number,
  token: string
): Promise<TestDetailResponse> => {
  return apiRequest(
    `${API_BASE_URL}/admin/tests/results/${testId}`,
    'GET',
    undefined,
    { Authorization: `Bearer ${token}` }
  );
};

// College management functions
export const getAllColleges = async (token: string): Promise<CollegesResponse> => {
  return apiRequest(
    COLLEGE_ENDPOINTS.GET_ALL_COLLEGES,
    'GET',
    undefined,
    { Authorization: `Bearer ${token}` }
  );
};

export const getCollege = async (id: string, token: string): Promise<{ college: CollegeData }> => {
  return apiRequest(
    COLLEGE_ENDPOINTS.GET_COLLEGE(id),
    'GET',
    undefined,
    { Authorization: `Bearer ${token}` }
  );
};

export const getCollegeUsers = async (id: string, token: string): Promise<CollegeUsersResponse> => {
  return apiRequest(
    COLLEGE_ENDPOINTS.GET_COLLEGE_USERS(id),
    'GET',
    undefined,
    { Authorization: `Bearer ${token}` }
  );
};

export const createCollege = async (collegeData: CollegeData, token: string): Promise<{ college: CollegeData }> => {
  return apiRequest(
    COLLEGE_ENDPOINTS.CREATE_COLLEGE,
    'POST',
    collegeData,
    { Authorization: `Bearer ${token}` }
  );
};

export const updateCollege = async (id: string, collegeData: Partial<CollegeData>, token: string): Promise<{ college: CollegeData }> => {
  return apiRequest(
    COLLEGE_ENDPOINTS.UPDATE_COLLEGE(id),
    'PUT',
    collegeData,
    { Authorization: `Bearer ${token}` }
  );
};

export const deleteCollege = async (id: string, token: string): Promise<{ message: string }> => {
  return apiRequest(
    COLLEGE_ENDPOINTS.DELETE_COLLEGE(id),
    'DELETE',
    undefined,
    { Authorization: `Bearer ${token}` }
  );
};

// Send proctoring event with camera snapshot
export const sendProctoringEvent = async (
  eventType: string,
  details: string,
  imageBlob: Blob,
  token: string
): Promise<any> => {
  // Create FormData object to match curl command format:
  // -F 'event_type=face_not_detected'
  // -F 'details=Student face not visible in camera'
  // -F 'event_photo=@/path/to/your/image.jpg'
  const formData = new FormData();
  formData.append('event_type', eventType);
  formData.append('details', details);
  formData.append('event_photo', imageBlob, 'proctoring_photo.jpg');

  console.log('Sending proctoring data via FormData:');
  console.log('- event_type:', eventType);
  console.log('- details:', details);
  console.log('- event_photo: [blob data with filename: proctoring_photo.jpg]');

  return fetch(TEST_ENDPOINTS.PROCTORING_EVENT, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`
    },
    body: formData
  }).then(response => {
    if (!response.ok) {
      throw new Error('Failed to send proctoring event');
    }
    return response.json();
  });
}; 