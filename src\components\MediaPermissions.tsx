import { useState, useEffect, useRef } from 'react';
import { Camera, CheckCircle } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { sendProctoringEvent } from '../utils/api';

interface MediaPermissionsProps {
  onPermissionsGranted: () => void;
}

const MediaPermissions = ({ onPermissionsGranted }: MediaPermissionsProps) => {
  const { token } = useAuth();
  const [permissions, setPermissions] = useState({
    camera: false
  });
  const [error, setError] = useState<string | null>(null);
  const [photoSent, setPhotoSent] = useState(false);
  const [showStartButton, setShowStartButton] = useState(false);
  const [isCapturing, setIsCapturing] = useState(false);
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  const requestPermissions = async () => {
    try {
      setIsCapturing(true);
      // Request only camera permissions, explicitly set audio to false
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: false
      });

      // Store the stream for cleanup
      streamRef.current = stream;

      // Set up video preview
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }

      // Update permissions state
      setPermissions({
        camera: true
      });
      
      // Give time for video to initialize before taking photo
      setTimeout(() => {
        captureAndSendPhoto();
      }, 1500);
      
    } catch (err) {
      console.error('Error requesting permissions:', err);
      setError('Failed to access camera. Please ensure you have granted the necessary permissions.');
      setIsCapturing(false);
    }
  };
  
  const captureAndSendPhoto = async () => {
    if (!videoRef.current || !canvasRef.current || !token) {
      setError('Could not capture your photo. Please try again.');
      setIsCapturing(false);
      return;
    }
    
    try {
      // Create canvas same size as video
      const video = videoRef.current;
      const canvas = canvasRef.current;
      
      // Make sure video has dimensions before capturing
      if (video.videoWidth === 0 || video.videoHeight === 0) {
        console.log("Video not ready yet, trying again shortly...");
        setTimeout(captureAndSendPhoto, 500);
        return;
      }
      
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      
      // Draw the current video frame to the canvas
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        
        // Convert canvas to blob
        canvas.toBlob(async (blob) => {
          if (blob && token) {
            try {
              await sendProctoringEvent(
                'test_started',  // event type
                'Initial student verification photo', // description
                blob,       // the image blob from canvas
                token       // JWT token for authentication
              );
              console.log('Student verification photo sent successfully.');
              
              // Show success and start button
              setPhotoSent(true);
              setShowStartButton(true);
              setIsCapturing(false);
              
            } catch (error) {
              console.error('Failed to send verification photo:', error);
              setError('Failed to send your verification photo. Please try again.');
              setIsCapturing(false);
            }
          } else {
            setError('Could not process your verification photo. Please try again.');
            setIsCapturing(false);
          }
        }, 'image/jpeg', 0.9); // JPEG with 90% quality for better image
      } else {
        setError('Could not process your photo. Please try again.');
        setIsCapturing(false);
      }
    } catch (error) {
      console.error('Error capturing photo:', error);
      setError('An error occurred while capturing your photo. Please try again.');
      setIsCapturing(false);
    }
  };

  // Cleanup function to stop all tracks when component unmounts
  useEffect(() => {
    return () => {
      if (streamRef.current) {
        // Ensure we properly clean up all media tracks
        streamRef.current.getTracks().forEach(track => {
          if (track.readyState === 'live') {
            track.stop();
            console.log(`Stopped ${track.kind} track in MediaPermissions cleanup`);
          }
        });
        streamRef.current = null;
        
        // Also clear video element's srcObject
        if (videoRef.current) {
          videoRef.current.srcObject = null;
        }
      }
    };
  }, []);

  const handleStartTest = () => {
    // Stop the camera stream before starting the test
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => {
        if (track.readyState === 'live') {
          track.stop();
        }
      });
      streamRef.current = null;
      
      if (videoRef.current) {
        videoRef.current.srcObject = null;
      }
    }
    
    // Call the parent's callback to continue
    onPermissionsGranted();
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-75 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-lg mx-auto overflow-y-auto max-h-[90vh]">
        <div className="text-center p-4 sm:p-6">
          <h2 className="text-lg sm:text-xl font-bold text-gray-900">Camera Access</h2>
          <p className="text-xs sm:text-sm text-gray-600 mt-1">
            We need access to your camera for proctoring the test
          </p>
        </div>

        {error && (
          <div className="mx-4 sm:mx-6 mb-4 bg-red-50 border-l-4 border-red-500 p-3 sm:p-4 rounded">
            <p className="text-xs sm:text-sm text-red-700">{error}</p>
          </div>
        )}

        {photoSent && (
          <div className="mx-4 sm:mx-6 mb-4 bg-green-50 border-l-4 border-green-500 p-3 sm:p-4 rounded flex items-start">
            <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-xs sm:text-sm text-green-700 font-semibold">Photo Captured Successfully</p>
              <p className="text-xs sm:text-sm text-green-600">Your initial verification photo has been captured. A final photo will be taken when you complete the test.</p>
            </div>
          </div>
        )}

        <div className="mx-4 sm:mx-6 mb-4">
          <div className="relative bg-gray-100 rounded-lg overflow-hidden aspect-video">
            {isCapturing && (
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 z-10">
                <div className="text-white text-center">
                  <div className="inline-block h-6 w-6 sm:h-8 sm:w-8 animate-spin rounded-full border-3 border-solid border-white border-r-transparent"></div>
                  <p className="mt-2 text-xs sm:text-sm">Capturing your photo...</p>
                </div>
              </div>
            )}
            <video
              ref={videoRef}
              autoPlay
              playsInline
              className="w-full h-full object-cover"
              style={{ transform: 'scaleX(-1)' }}
            />
            
            {/* Face alignment frame */}
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <div className="relative w-2/5 h-3/5">
                {/* Oval face outline */}
                <div className="absolute inset-0 border-3 sm:border-4 border-blue-400 rounded-full opacity-70"></div>
                {/* Guidelines */}
                <div className="absolute left-1/2 top-0 bottom-0 w-px bg-blue-400 opacity-40 transform -translate-x-1/2"></div>
                <div className="absolute top-1/2 left-0 right-0 h-px bg-blue-400 opacity-40 transform -translate-y-1/2"></div>
              </div>
            </div>
            
            {/* Face alignment message */}
            <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white text-center py-1 px-2">
              <p className="text-xs sm:text-sm">Please align your face within the oval frame</p>
            </div>
            
            {/* Hidden canvas for photo capture */}
            <canvas ref={canvasRef} style={{ display: 'none' }} />
          </div>
        </div>

        <div className="mx-4 sm:mx-6 space-y-3">
          <div className="flex items-center">
            <div className={`h-3 w-3 sm:h-4 sm:w-4 rounded-full mr-2 ${permissions.camera ? 'bg-green-500' : 'bg-gray-300'}`}></div>
            <span className="text-xs sm:text-sm">Camera: {permissions.camera ? 'Allowed' : 'Pending'}</span>
          </div>
        </div>

        <div className="p-4 sm:p-6">
          <p className="text-xs sm:text-sm text-gray-500 mb-4">
            This test requires camera access for proctoring purposes. 
            Your video feed will be monitored during the test to ensure academic integrity.
          </p>
          
          {showStartButton ? (
            <button
              onClick={handleStartTest}
              className="w-full inline-flex justify-center items-center px-4 py-2 sm:py-3 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Start Test
            </button>
          ) : (
            <button
              onClick={requestPermissions}
              disabled={permissions.camera || isCapturing}
              className="w-full inline-flex justify-center items-center px-4 py-2 sm:py-3 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isCapturing ? (
                <>
                  <span className="mr-2 h-3 w-3 sm:h-4 sm:w-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                  Processing...
                </>
              ) : permissions.camera ? (
                <>
                  <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                  Permissions Granted
                </>
              ) : (
                'Grant Permissions'
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default MediaPermissions; 