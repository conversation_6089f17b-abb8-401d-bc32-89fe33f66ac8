export interface User {
  id: string;
  name: string;
  mobile: string;
  rollNumber: string;
  role: 'student' | 'admin';
  email?: string;
}

export interface AuthContextType {
  currentUser: User | null;
  token: string | null;
  login: (identifier: string, password: string, role: 'student' | 'admin') => Promise<boolean>;
  logout: () => void;
  loading: boolean;
  setCurrentUser: (user: User | null) => void;
  setToken: (token: string | null) => void;
}

export interface LoginResponse {
  message: string;
  token: string;
  user: {
    id: number;
    name: string;
    email: string;
    role: string;
    mobile?: string;
  }
}