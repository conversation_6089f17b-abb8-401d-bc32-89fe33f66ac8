import { useEffect, useState, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronLeft, ChevronRight, AlertTriangle, CheckCircle, X, Info, Camera, Maximize, Minimize } from 'lucide-react';
import { useTest } from '../../contexts/TestContext';
import { useAuth } from '../../contexts/AuthContext';
import QuestionCard from '../../components/QuestionCard';
import QuestionNavigation from '../../components/QuestionNavigation';
import TestTimer from '../../components/TestTimer';
import ProctoringSidebar, { ProctoringSidebarHandle } from '../../components/ProctoringSidebar';
import MediaPermissions from '../../components/MediaPermissions';
import { enableTabRestriction, disableTabRestriction } from '../../utils/tabRestriction';
import toast from 'react-hot-toast';
import { sendProctoringEvent } from '../../utils/api';

const TestPage = () => {
  // Clear any tab change tracking immediately on component load
  useEffect(() => {
    localStorage.removeItem('tabChangeCount');
    disableTabRestriction();
  }, []);
  
  const { 
    questions, 
    currentQuestionIndex, 
    answers, 
    testStarted,
    testEnded,
    remainingTime,
    prepareTest, 
    startTest,
    answerQuestion, 
    goToNextQuestion, 
    goToPreviousQuestion, 
    goToQuestion,
    endTest
  } = useTest();
  
  const { currentUser, token } = useAuth();
  const navigate = useNavigate();
  
  const [violations, setViolations] = useState<string[]>([]);
  const [showInstructions, setShowInstructions] = useState(true);
  const [showPermissions, setShowPermissions] = useState(false);
  const [isTestCompleted, setIsTestCompleted] = useState(false);
  const [showThankYouPopup, setShowThankYouPopup] = useState(false);
  const [tabChanges, setTabChanges] = useState(0);
  const [alreadyCompletedModal, setAlreadyCompletedModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState('You have already completed the test.');
  const [showIncompleteWarning, setShowIncompleteWarning] = useState(false);
  const [countdown, setCountdown] = useState(5);
  const [keyboardWarnings, setKeyboardWarnings] = useState(0);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [showFullScreenWarning, setShowFullScreenWarning] = useState(false);
  
  // Reference to the container element for full-screen
  const fullScreenRef = useRef<HTMLDivElement>(null);
  
  // Reference to access the final screenshot function directly
  const proctoringSidebarRef = useRef<ProctoringSidebarHandle>(null);
  
  // Reference to track when we last showed a toast for each violation type
  const toastThrottleRef = useRef<Record<string, number>>({});
  
  // Function to enter full-screen mode
  const enterFullScreen = useCallback(async () => {
    const element = fullScreenRef.current || document.documentElement;
    
    try {
      if (element.requestFullscreen) {
        await element.requestFullscreen();
      } else if ((element as any).mozRequestFullScreen) { /* Firefox */
        await (element as any).mozRequestFullScreen();
      } else if ((element as any).webkitRequestFullscreen) { /* Chrome, Safari & Opera */
        await (element as any).webkitRequestFullscreen();
      } else if ((element as any).msRequestFullscreen) { /* IE/Edge */
        await (element as any).msRequestFullscreen();
      }
      
      setIsFullScreen(true);
      
      // Show a success toast
      toast.success('Full-screen mode activated', {
        duration: 3000,
        icon: <Maximize className="text-green-500" />,
      });
    } catch (error) {
      console.error('Error entering full-screen:', error);
      
      // Show error toast
      toast.error('Failed to enter full-screen mode. Please try again.', {
        duration: 5000,
        icon: <AlertTriangle className="text-red-500" />,
      });
    }
  }, []);
  
  // Function to exit full-screen mode
  const exitFullScreen = useCallback(() => {
    try {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if ((document as any).mozCancelFullScreen) { /* Firefox */
        (document as any).mozCancelFullScreen();
      } else if ((document as any).webkitExitFullscreen) { /* Chrome, Safari & Opera */
        (document as any).webkitExitFullscreen();
      } else if ((document as any).msExitFullscreen) { /* IE/Edge */
        (document as any).msExitFullscreen();
      }
      
      setIsFullScreen(false);
    } catch (error) {
      console.error('Error exiting full-screen:', error);
    }
  }, []);
  
  // Handle full-screen change events
  useEffect(() => {
    const handleFullScreenChange = () => {
      const isCurrentlyFullScreen = 
        document.fullscreenElement || 
        (document as any).mozFullScreenElement || 
        (document as any).webkitFullscreenElement || 
        (document as any).msFullscreenElement;
      
      setIsFullScreen(!!isCurrentlyFullScreen);
      
      // If test is running and user exits full-screen
      if (!isCurrentlyFullScreen && testStarted && !testEnded && !isTestCompleted && !showInstructions) {
        // Show warning popup
        setShowFullScreenWarning(true);
        
        // Add to violations
        setViolations(prev => [...prev, 'Exited full-screen mode']);
      }
    };
    
    // Add event listeners for all browsers
    document.addEventListener('fullscreenchange', handleFullScreenChange);
    document.addEventListener('mozfullscreenchange', handleFullScreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullScreenChange);
    document.addEventListener('MSFullscreenChange', handleFullScreenChange);
    
    return () => {
      // Remove event listeners when component unmounts
      document.removeEventListener('fullscreenchange', handleFullScreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullScreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullScreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullScreenChange);
    };
  }, [testStarted, testEnded, isTestCompleted, showInstructions]);
  
  // Start full-screen mode when test starts
  useEffect(() => {
    if (testStarted && !showInstructions && !isFullScreen && !testEnded && !isTestCompleted) {
      enterFullScreen();
    }
    
    // Exit full-screen when test ends
    if ((testEnded || isTestCompleted) && isFullScreen) {
      exitFullScreen();
    }
  }, [testStarted, showInstructions, isFullScreen, testEnded, isTestCompleted, enterFullScreen, exitFullScreen]);
  
  useEffect(() => {
    // Prepare the test when the component mounts
    prepareTest();
  }, [prepareTest]);
  
  useEffect(() => {
    // Show thank you popup if test is ended instead of immediate navigation
    if (testEnded) {
      setIsTestCompleted(true);
      setShowThankYouPopup(true);
      setCountdown(5);
    }
  }, [testEnded, navigate]);
  
  useEffect(() => {
    if (!showThankYouPopup) return;
    
    if (countdown <= 0) {
      handleGoToLandingPage();
      return;
    }
    
    const timer = setTimeout(() => {
      setCountdown(prev => prev - 1);
    }, 1000);
    
    return () => clearTimeout(timer);
  }, [countdown, showThankYouPopup]);
  
  // Handle tab switching prevention - ONLY for active test, not during instructions
  useEffect(() => {
    let cleanupTabRestriction: (() => void) | null = null;
    
    // Only add tab change detection when test has actually started (after instructions)
    if (testStarted && !showInstructions && !testEnded && !isTestCompleted) {
      // Check if we already have an active restriction - avoid unnecessary calls
      const hasRestriction = cleanupTabRestriction !== null;
      if (!hasRestriction) {
        console.log('Enabling tab restriction - first time setup');
        
        // Get existing tab count instead of clearing it
        let storedCount = 0;
        try {
          const stored = localStorage.getItem('tabChangeCount');
          if (stored) {
            storedCount = parseInt(stored, 10);
            // Update component state with stored count
            setTabChanges(storedCount);
          }
        } catch (e) {
          console.error('Error reading tab count', e);
        }
        
        cleanupTabRestriction = enableTabRestriction((count) => {
          console.log('Tab change callback triggered, count:', count);
          
          // Always update the tab changes count
          setTabChanges(count);
          setViolations(prev => [...prev, 'Tab change detected']);
          
          // Show warning when user returns to the tab
          toast.error('Changing tabs during the test is not allowed!', {
            duration: 5000,
            icon: <AlertTriangle className="text-red-500" />,
            id: `tab-change-${count}`, // Use a unique ID to prevent duplicates
          });
          
          // Auto-disqualify after 5 tab changes
          if (count >= 5) {
            toast.error('Test disqualified! You changed tabs more than 5 times.', {
              duration: 8000,
              icon: <AlertTriangle className="text-red-500" />,
              id: 'disqualification-notice', // Use a unique ID to prevent duplicates
            });
            
            // Set test as completed
            setIsTestCompleted(true);
            
            // End the test first to trigger the final screenshot
            endTest('disqualified').then(() => {
              // Clean up resources after a delay
              setTimeout(() => {
                cleanupAfterTest();
                
                // Automatically navigate to registration page
                try {
                  navigate('/', { replace: true });
                } catch (err) {
                  console.error('Error redirecting after disqualification:', err);
                }
              }, 2000);
            }).catch(err => {
              console.error("Error ending test after disqualification:", err);
              cleanupAfterTest();
              
              // Still try to navigate away
              try {
                navigate('/', { replace: true });
              } catch (err) {
                console.error('Error redirecting after disqualification:', err);
              }
            });
          }
        });
      }
    } else {
      // Remove tab restrictions when not in the actual test
      console.log('Disabling tab restriction');
      disableTabRestriction();
      cleanupTabRestriction = null;
    }
    
    return () => {
      if (cleanupTabRestriction) {
        cleanupTabRestriction();
        cleanupTabRestriction = null;
      }
    };
  }, [testStarted, testEnded, isTestCompleted, showInstructions, setViolations, endTest, navigate]);
  
  // Handle page navigation/refresh to clean up camera resources
  useEffect(() => {
    const handleBeforeUnload = () => {
      // Stop any media streams when navigating away or refreshing
      const mediaElements = document.querySelectorAll('video, audio');
      mediaElements.forEach(media => {
        const mediaStream = (media as HTMLVideoElement | HTMLAudioElement).srcObject as MediaStream;
        if (mediaStream) {
          mediaStream.getTracks().forEach(track => {
            if (track.readyState === 'live') {
              track.stop();
            }
          });
        }
      });
    };

    // Add both beforeunload and popstate event listeners
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('popstate', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('popstate', handleBeforeUnload);
      handleBeforeUnload(); // Clean up on component unmount as well
    };
  }, []);
  
  // Handle permissions
  const handleStartTest = () => {
    setShowPermissions(true);
  };
  
  const handlePermissionsGranted = async () => {
    setShowPermissions(false);
    try {
      // Try to start the test now that permissions are granted and photo is captured
      await startTest();
      
      // Only hide instructions after the test has successfully started
    setShowInstructions(false);
      
      // Enter full-screen mode automatically
      enterFullScreen();
      
      console.log('Test started successfully, proctoring will begin soon');
      
    } catch (err: any) {
      // Check if the error response has the expected structure
      const errorResponse = err?.response?.data || err;
      const status = errorResponse?.status;
      const message = errorResponse?.message || err?.message || 'You have already completed the test.';
      
      // If error message indicates already completed (status "1" or message contains "already completed")
      if (status === "1" || message.toLowerCase().includes('already completed')) {
        setErrorMessage(message);
        setAlreadyCompletedModal(true);
        setShowInstructions(true); // Ensure spinner is not shown
      } else {
        toast.error('Failed to start test. Please try again.');
        setShowInstructions(true); // Ensure spinner is not shown
      }
    }
  };
  
  // Direct capture function that can be called from submission handlers
  const captureFinalScreenshot = useCallback(async () => {
    try {
      console.log("Manually triggering final screenshot capture");
      
      // Use token from component level, not from hook called inside
      if (!token) {
        console.error("Cannot send screenshot - no authentication token available");
        return false;
      }
      
      // Try both approaches to ensure the screenshot is captured
      
      // 1. Direct call if reference is available
      if (proctoringSidebarRef.current?.captureAndSendSnapshot) {
        console.log("Calling captureAndSendSnapshot via ref");
        await proctoringSidebarRef.current.captureAndSendSnapshot('test_completed', 'Final verification photo at test submission');
        console.log("Final screenshot captured via direct ref call");
        return true;
      } else {
        console.log("No direct ref to ProctoringSidebar available");
        
        // 2. Try to use the API directly as fallback
        try {
          // Try to get the video element directly
          const videoElements = document.querySelectorAll('video');
          if (videoElements.length > 0) {
            const video = videoElements[0];
            
            // Only proceed if video has dimensions
            if (video.videoWidth > 0 && video.videoHeight > 0) {
              // Create a temporary canvas
              const tempCanvas = document.createElement('canvas');
              tempCanvas.width = video.videoWidth;
              tempCanvas.height = video.videoHeight;
              
              // Draw the current video frame to the temp canvas
              const ctx = tempCanvas.getContext('2d');
              if (ctx) {
                ctx.drawImage(video, 0, 0, tempCanvas.width, tempCanvas.height);
                
                // Convert canvas to blob
                return new Promise<boolean>((resolve) => {
                  tempCanvas.toBlob(async (blob) => {
                    if (blob && token) {
                      try {
                        await sendProctoringEvent(
                          'test_completed',
                          'Final verification photo at test submission (direct capture)',
                          blob,
                          token
                        );
                        console.log("Final screenshot sent via direct API call");
                        resolve(true);
                      } catch (error) {
                        console.error("Failed to send screenshot via direct API call:", error);
                        resolve(false);
                      } finally {
                        tempCanvas.remove();
                      }
                    } else {
                      console.error("Failed to create blob or missing token");
                      resolve(false);
                      tempCanvas.remove();
                    }
                  }, 'image/jpeg', 0.9);
                });
              }
            } else {
              console.error("Video not ready (no dimensions)");
            }
          } else {
            console.error("No video elements found for fallback capture");
          }
        } catch (error) {
          console.error("Error in direct API capture:", error);
        }
      }
      
      // Set the test as completed to trigger the useEffect in ProctoringSidebar
      setIsTestCompleted(true);
      
      return false;
    } catch (error) {
      console.error("Error capturing final screenshot:", error);
      return false;
    }
  }, [token]);
  
  const handleSubmitTest = () => {
    // Check if all questions are answered
    const unansweredCount = questions.length - Object.keys(answers).length;
    
    if (unansweredCount > 0) {
      // Show warning if not all questions are answered
      setShowIncompleteWarning(true);
      return;
    }
    
    // All questions answered, proceed with submission
    if (window.confirm('Are you sure you want to submit the test? You cannot change your answers after submission.')) {
      // Notify user that final screenshot is being captured
      toast.success('Capturing final verification photo...', {
        duration: 2000,
        icon: <Camera className="text-blue-500" />,
      });
      
      // Create a flag to track if we're actively processing the submission
      let isProcessingSubmission = true;
      
      // Take final screenshot first before ending test
      captureFinalScreenshot()
        .then(screenshotSuccess => {
          console.log(`Screenshot capture ${screenshotSuccess ? 'successful' : 'attempted with fallback'}, now proceeding with test submission`);
          
          // Set a flag to prevent multiple submissions
          if (!isProcessingSubmission) return;
          isProcessingSubmission = false;
          
          // Mark test as completed to update UI state
          setIsTestCompleted(true);
          
          // End the test and show thank you popup
          return endTest()
            .then(() => {
              // Show thank you popup after test is ended
              setShowThankYouPopup(true);
              setCountdown(5);
              
              // Clean up resources
              cleanupAfterTest();
            })
            .catch(err => {
              console.error("Error ending test:", err);
              // Still show thank you popup even if API fails
              setShowThankYouPopup(true);
              setCountdown(5);
              
              // Clean up resources
              cleanupAfterTest();
            });
        })
        .catch(err => {
          console.error("Error during test submission:", err);
          
          // Prevent duplicate processing
          if (!isProcessingSubmission) return;
          isProcessingSubmission = false;
          
          // Show error toast
          toast.error('There was an error submitting your test, but your answers have been saved', {
            duration: 3000
          });
          
          // Still end the test even if screenshot fails
          setIsTestCompleted(true);
          
          endTest()
            .then(() => {
              setShowThankYouPopup(true);
              setCountdown(5);
              cleanupAfterTest();
            })
            .catch(submitError => {
              console.error("Failed to end test after screenshot error:", submitError);
              setShowThankYouPopup(true);
              setCountdown(5);
              cleanupAfterTest();
            });
        });
    }
  };
  
  // Helper function to clean up after test completion
  const cleanupAfterTest = useCallback(() => {
    // After a delay to ensure screenshot is captured, clean up resources
    setTimeout(() => {
      // Stop all media tracks
      const mediaElements = document.querySelectorAll('video, audio');
      mediaElements.forEach(media => {
        const mediaStream = (media as HTMLVideoElement | HTMLAudioElement).srcObject as MediaStream;
        if (mediaStream) {
          mediaStream.getTracks().forEach(track => {
            if (track.readyState === 'live') {
              track.stop();
            }
          });
        }
      });
      
      // Remove tab change restrictions
      disableTabRestriction();
      
      // Clear local storage data
      localStorage.removeItem('testAppUser');
      localStorage.removeItem('testAppToken');
      localStorage.removeItem('tabChangeCount');
    }, 2000); // Delay to ensure screenshot is captured
  }, []);
  
  const handleGoToLandingPage = () => {
    // Exit full-screen before navigating away
    if (isFullScreen) {
      exitFullScreen();
    }
    
    // Close the popup
    setShowThankYouPopup(false);
    
    // Clear local storage when test is completed
    localStorage.removeItem('testAppUser');
    localStorage.removeItem('testAppToken');
    localStorage.removeItem('tabChangeCount');
    
    // Try multiple approaches to close the window
    try {
      // First navigate away (as a fallback)
      navigate('/', { replace: true });
      
      // Try to close the current window
      window.close();
      
      // Try to close parent window (if opened via JavaScript)
      if (window.parent && window.parent !== window) {
        window.parent.close();
      }
      
      // Try to close the top-most window
      if (window.top && window.top !== window) {
        window.top.close();
      }
      
      // If all else fails, replace content with a message
      document.body.innerHTML = '<div style="text-align:center; padding:20px;"><h1>Test Completed</h1><p>You may now close this window.</p></div>';
      // close the full window
      window.close();
      
    } catch (e) {
      console.error('Error closing window:', e);
      // Still try to navigate away if closing fails
      navigate('/', { replace: true });
    }
  };
  
  const handleViolation = (type: string) => {
    // Filter out any microphone-related violations since we're not using microphone
    if (type === 'background_noise' || type.includes('mic') || type.includes('audio')) {
      console.log('Ignoring microphone-related violation:', type);
      return;
    }
    
    // Prevent adding duplicate violations of the same type in sequence
    if (violations.length > 0 && violations[violations.length - 1] === type) {
      console.log('Ignoring duplicate violation:', type);
      return;
    }
    
    // Limit the number of violations to the 5 most recent
    setViolations(prev => {
      const newViolations = [...prev, type];
      // Only keep the 5 most recent violations
      return newViolations.slice(-5);
    });
    
    // Throttle violation toasts - store the last time we showed a toast for this type
    const now = Date.now();
    const lastToastTime = toastThrottleRef.current[type] || 0;
    
    // Only show a toast if it's been at least 10 seconds since the last one for this type
    if (now - lastToastTime >= 10000) {
    toast.error('Test violation detected', {
      duration: 3000,
      icon: <AlertTriangle className="text-red-500" />,
        id: `violation-${type}-${now}` // Use a unique ID to prevent duplicates
      });
      
      // Update the last toast time for this violation type
      toastThrottleRef.current[type] = now;
    }
  };
  
  // Handle keyboard and right-click restrictions
  useEffect(() => {
    // Only apply restrictions when test has started and is not ended
    if (!testStarted || testEnded || isTestCompleted || showInstructions) {
      return;
    }
    
    // Handle restricted keyboard shortcuts
    const handleKeyDown = (e: KeyboardEvent) => {
      // Prevent Alt+F4 and Alt+Tab (limited in browser)
      if (e.altKey && (e.key === 'F4' || e.key === 'Tab')) {
        e.preventDefault();
        console.log('Alt+F4 or Alt+Tab disabled');
        
        // Track keyboard shortcut attempts
        setKeyboardWarnings(prev => {
          const newCount = prev + 1;
          
          // Show warning toast
          toast.error(`Keyboard shortcuts are disabled during the test! (${newCount}/5)`, {
            duration: 3000,
            icon: <AlertTriangle className="text-red-500" />,
          });
          
          // Add to violations
          setViolations(prev => [...prev, 'Restricted keyboard shortcut used']);
          
          // Auto-disqualify after 5 attempts
          if (newCount >= 5) {
            toast.error('Test disqualified! You used restricted keyboard shortcuts too many times.', {
              duration: 8000,
              icon: <AlertTriangle className="text-red-500" />,
            });
            
            // Set test as completed
            setIsTestCompleted(true);
            
            // End the test first to trigger the final screenshot
            endTest('disqualified').then(() => {
              // Clean up resources after a delay
              setTimeout(() => {
                cleanupAfterTest();
                
                // Automatically navigate to registration page
                try {
                  navigate('/', { replace: true });
                } catch (err) {
                  console.error('Error redirecting after disqualification:', err);
                }
              }, 2000);
            }).catch(err => {
              console.error("Error ending test after disqualification:", err);
              cleanupAfterTest();
              
              // Still try to navigate away
              try {
                navigate('/', { replace: true });
              } catch (err) {
                console.error('Error redirecting after disqualification:', err);
              }
            });
          }
          
          return newCount;
        });
        return;
      }
      
      // Prevent Escape key
      if (e.key === 'Escape') {
        e.preventDefault();
        return;
      }
      
      // Prevent Ctrl+Shift+I (DevTools)
      if (e.ctrlKey && e.shiftKey && e.key.toLowerCase() === 'i') {
        e.preventDefault();
        return;
      }
      
      // Prevent F12 (DevTools)
      if (e.key === 'F12') {
        e.preventDefault();
        return;
      }
    };
    
    // Prevent right-click context menu
    const handleContextMenu = (e: MouseEvent) => {
      e.preventDefault();
      
      // Show notification about right-click being disabled
      toast.error('Right-click is disabled during the test', {
        duration: 2000,
        icon: <AlertTriangle className="text-red-500" />,
      });
      
      return false;
    };
    
    // Add event listeners
    window.addEventListener('keydown', handleKeyDown, { capture: true });
    document.addEventListener('contextmenu', handleContextMenu);
    
    // Clean up event listeners on unmount
    return () => {
      window.removeEventListener('keydown', handleKeyDown, { capture: true });
      document.removeEventListener('contextmenu', handleContextMenu);
    };
  }, [testStarted, testEnded, isTestCompleted, showInstructions, setViolations, endTest]);
  
  // Show Thank You popup
  if (showThankYouPopup) {
    const isDisqualified = tabChanges >= 5 || keyboardWarnings >= 5;
    
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-75">
        <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
          <div className="text-center">
            <div className={`mx-auto flex items-center justify-center h-12 w-12 rounded-full ${
              isDisqualified ? 'bg-red-100' : 'bg-green-100'
            }`}>
              {isDisqualified ? (
                <X className="h-6 w-6 text-red-600" />
              ) : (
                <CheckCircle className="h-6 w-6 text-green-600" />
              )}
            </div>
            <h3 className="mt-4 text-lg font-medium text-gray-900">
              {isDisqualified ? 'Test Disqualified' : `Test Completed Successfully`}
            </h3>
            <p className="mt-2 text-sm text-gray-500">
              {isDisqualified 
                ? tabChanges >= 5 
                  ? 'Your test has been disqualified because you changed tabs or refreshed the page more than 5 times.'
                  : 'Your test has been disqualified because you used restricted keyboard shortcuts too many times.'
                : `Thank you ${currentUser?.name || ''} for completing the test. Your responses have been recorded.`}
            </p>
            <div className="mt-6">
              {/* <button
                onClick={handleGoToLandingPage}
                className={`inline-flex justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white ${
                  isDisqualified ? 'bg-red-600 hover:bg-red-700' : 'bg-blue-600 hover:bg-blue-700'
                } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`}
              >
                Thank You ({countdown}s)
              </button> */}
            </div>
            <p className="mt-3 text-sm text-gray-400">
              This window will close automatically in {countdown} seconds
            </p>
          </div>
        </div>
      </div>
    );
  }
  
  // Show already completed modal
  if (alreadyCompletedModal) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-75">
        <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100">
              <AlertTriangle className="h-6 w-6 text-yellow-600" />
            </div>
            <h3 className="mt-4 text-lg font-medium text-gray-900">
              {errorMessage}
            </h3>
            <div className="mt-6">
              <button
                onClick={handleGoToLandingPage}
                className="inline-flex justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Return to Home
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  // Show instructions before starting the test
  if (showInstructions) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col">
        <div className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <h1 className="text-2xl font-bold text-gray-900">Test Instructions</h1>
          </div>
        </div>
        
        <div className="flex-1 max-w-4xl mx-auto w-full px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Welcome to the Campus Interview Test</h2>
              
              <div className="space-y-4 text-gray-600">
                <p>Please read the following instructions carefully before starting the test:</p>
                
                <ol className="list-decimal pl-5 space-y-2">
                  <li>The test consists of 45 questions to be completed in 60 minutes.</li>
                  <li>Questions are divided into logical reasoning, Algebraic Word Problems, Blood Relations, Circular Arrangements, Clocks & Calendars, Critical Reasoning, Cube & Dice (3D Spatial Reasoning), Data Interpretation, Data Sufficiency, Direction Sense & Navigation, Functions & Graphs, Input–Output / Machine Reasoning, Logical Reasoning Puzzles, Mensuration, Mixtures & Alligations, Number Properties & Divisibility, Percentages & Interest, Probability & Expected Value, Profit & Loss, Progressions categories.</li>
                  <li>All questions are objective type (multiple choice).</li>
                  <li>You can navigate between questions using the navigation panel.</li>
                  <li>The test will automatically submit when the time expires.</li>
                  <li><strong>Camera access is required</strong> for proctoring purposes.</li>
                  <li>You must remain in view of the camera throughout the test.</li>
                  <li>No other person should be visible in the frame.</li>
                  <li>Maintain a quiet environment during the test.</li>
                  <li><strong>Do not refresh or change tabs during the test</strong>. Refreshing the page or changing tabs more than three times will result in automatic disqualification.</li>
                  <li>You cannot return to the test after submission.</li>
                </ol>
                
                <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mt-6">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <AlertTriangle className="h-5 w-5 text-yellow-400" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-yellow-700">
                        By starting the test, you agree to be monitored through your camera.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-50 px-6 py-4 flex justify-end">
              <button
                type="button"
                onClick={handleStartTest}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Start Test
              </button>
            </div>
          </div>
        </div>
        
        {showPermissions && (
          <MediaPermissions onPermissionsGranted={handlePermissionsGranted} />
        )}
      </div>
    );
  }
  
  if (!testStarted || questions.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Preparing your test...</p>
        </div>
      </div>
    );
  }
  
  const currentQuestion = questions[currentQuestionIndex];
  const attemptedCount = Object.keys(answers).length;
  
  // Show incomplete warning modal
  if (showIncompleteWarning) {
    const unansweredCount = questions.length - Object.keys(answers).length;
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-75">
        <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-orange-100">
              <Info className="h-6 w-6 text-orange-600" />
            </div>
            <h3 className="mt-4 text-lg font-medium text-gray-900">
              Incomplete Test
            </h3>
            <p className="mt-2 text-sm text-gray-500">
              You have {unansweredCount} unanswered {unansweredCount === 1 ? 'question' : 'questions'}. 
              Would you like to complete the remaining questions or submit the test with only the answered questions?
            </p>
            <div className="mt-6 flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-3 justify-center">
              <button
                onClick={() => setShowIncompleteWarning(false)}
                className="inline-flex justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Continue Test
              </button>
              <button
                onClick={() => {
                  setShowIncompleteWarning(false);
                  
                  // Notify user that final screenshot is being captured
                  toast.success('Capturing final verification photo...', {
                    duration: 2000,
                    icon: <Camera className="text-blue-500" />,
                  });
                  
                  // Create a flag to track if we're actively processing the submission
                  let isProcessingSubmission = true;
                  
                  // Take final screenshot first before ending test
                  captureFinalScreenshot()
                    .then(screenshotSuccess => {
                      console.log(`Screenshot capture ${screenshotSuccess ? 'successful' : 'attempted with fallback'}, now proceeding with test submission`);
                      
                      // Set a flag to prevent multiple submissions
                      if (!isProcessingSubmission) return;
                      isProcessingSubmission = false;
                      
                      // Mark test as completed to update UI state
                      setIsTestCompleted(true);
                      
                      // End the test and show thank you popup
                      return endTest()
                        .then(() => {
                          // Show thank you popup after test is ended
                          setShowThankYouPopup(true);
                          setCountdown(5);
                          
                          // Clean up resources
                          cleanupAfterTest();
                        })
                        .catch(err => {
                          console.error("Error ending test:", err);
                          // Still show thank you popup even if API fails
                          setShowThankYouPopup(true);
                          setCountdown(5);
                          
                          // Clean up resources
                          cleanupAfterTest();
                        });
                    })
                    .catch(err => {
                      console.error("Error during test submission:", err);
                      
                      // Prevent duplicate processing
                      if (!isProcessingSubmission) return;
                      isProcessingSubmission = false;
                      
                      // Show error toast
                      toast.error('There was an error submitting your test, but your answers have been saved', {
                        duration: 3000
                      });
                      
                      // Still end the test even if screenshot fails
                      setIsTestCompleted(true);
                      
                      endTest()
                        .then(() => {
                          setShowThankYouPopup(true);
                          setCountdown(5);
                          cleanupAfterTest();
                        })
                        .catch(submitError => {
                          console.error("Failed to end test after screenshot error:", submitError);
                          setShowThankYouPopup(true);
                          setCountdown(5);
                          cleanupAfterTest();
                        });
                    });
                }}
                className="inline-flex justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Submit Anyway
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  // Full-screen exit warning popup
  if (showFullScreenWarning) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-75">
        <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100">
              <AlertTriangle className="h-6 w-6 text-yellow-600" />
            </div>
            <h3 className="mt-4 text-lg font-medium text-gray-900">
              Full-Screen Mode Exited
            </h3>
            <p className="mt-2 text-sm text-gray-500">
              You have exited full-screen mode. This may be considered a violation of test rules.
              Would you like to return to full-screen mode and continue the test, or submit your test now?
            </p>
            <div className="mt-6 flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-3 justify-center">
              <button
                onClick={() => {
                  setShowFullScreenWarning(false);
                  
                  // Notify user that final screenshot is being captured
                  toast.success('Capturing final verification photo...', {
                    duration: 2000,
                    icon: <Camera className="text-blue-500" />,
                  });
                  
                  // Create a flag to track if we're actively processing the submission
                  let isProcessingSubmission = true;
                  
                  // Take final screenshot first before ending test
                  captureFinalScreenshot()
                    .then(screenshotSuccess => {
                      console.log(`Screenshot capture ${screenshotSuccess ? 'successful' : 'attempted with fallback'}, now proceeding with test submission`);
                      
                      // Set a flag to prevent multiple submissions
                      if (!isProcessingSubmission) return;
                      isProcessingSubmission = false;
                      
                      // Mark test as completed to update UI state
                      setIsTestCompleted(true);
                      
                      // End the test and show thank you popup
                      return endTest()
                        .then(() => {
                          // Show thank you popup after test is ended
                          setShowThankYouPopup(true);
                          setCountdown(5);
                          
                          // Clean up resources
                          cleanupAfterTest();
                        })
                        .catch(err => {
                          console.error("Error ending test:", err);
                          // Still show thank you popup even if API fails
                          setShowThankYouPopup(true);
                          setCountdown(5);
                          
                          // Clean up resources
                          cleanupAfterTest();
                        });
                    })
                    .catch(err => {
                      console.error("Error during test submission:", err);
                      
                      // Prevent duplicate processing
                      if (!isProcessingSubmission) return;
                      isProcessingSubmission = false;
                      
                      // Show error toast
                      toast.error('There was an error submitting your test, but your answers have been saved', {
                        duration: 3000
                      });
                      
                      // Still end the test even if screenshot fails
                      setIsTestCompleted(true);
                      
                      endTest()
                        .then(() => {
                          setShowThankYouPopup(true);
                          setCountdown(5);
                          cleanupAfterTest();
                        })
                        .catch(submitError => {
                          console.error("Failed to end test after screenshot error:", submitError);
                          setShowThankYouPopup(true);
                          setCountdown(5);
                          cleanupAfterTest();
                        });
                    });
                }}
                className="inline-flex justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Submit Test
              </button>
              <button
                onClick={() => {
                  setShowFullScreenWarning(false);
                  enterFullScreen();
                }}
                className="inline-flex justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Continue Test
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div ref={fullScreenRef} className="min-h-screen bg-gray-50 flex flex-col">
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex flex-col sm:flex-row justify-between items-center">
          <div className="mb-2 sm:mb-0 text-center sm:text-left">
            <h1 className="text-xl font-semibold text-gray-900">Campus Interview Test</h1>
            <p className="text-sm text-gray-500">
              {currentUser?.name} ({currentUser?.rollNumber})
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
          <TestTimer 
            remainingTime={remainingTime} 
            onTimeEnd={endTest} 
          />
            
            {/* Full-screen toggle button */}
            <button
              onClick={isFullScreen ? exitFullScreen : enterFullScreen}
              className="inline-flex items-center justify-center p-2 rounded-full bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              title={isFullScreen ? "Exit full-screen" : "Enter full-screen"}
            >
              {isFullScreen ? (
                <Minimize className="h-5 w-5 text-gray-600" />
              ) : (
                <Maximize className="h-5 w-5 text-gray-600" />
              )}
            </button>
          </div>
        </div>
      </div>
      
      {tabChanges > 0 && (
        <div className="bg-red-50 border-b border-red-200 px-4 py-2">
          <div className="max-w-7xl mx-auto flex items-center">
            <AlertTriangle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" />
            <p className="text-sm text-red-700">
              Warning: You have changed tabs {tabChanges} {tabChanges === 1 ? 'time' : 'times'}. Switching tabs more than 4 times will result in disqualification on the 5th attempt.
            </p>
          </div>
        </div>
      )}
      
      <div className="flex-1 flex flex-col lg:flex-row">
        <div className="flex-1 py-6 overflow-y-auto">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6">
            <QuestionCard
              question={currentQuestion}
              questionNumber={currentQuestionIndex + 1}
              selectedAnswer={answers[currentQuestionIndex]}
              onAnswerSelect={(answer) => answerQuestion(currentQuestionIndex, answer)}
            />
            
            <div className="flex justify-between items-center">
              <button
                onClick={goToPreviousQuestion}
                disabled={currentQuestionIndex === 0}
                className="inline-flex items-center px-3 sm:px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronLeft className="mr-1 sm:mr-2 h-4 w-4" />
                <span className="hidden xs:inline">Previous</span>
              </button>
              
              {currentQuestionIndex === questions.length - 1 ? (
                <button
                  onClick={handleSubmitTest}
                  className="inline-flex items-center px-3 sm:px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Submit Test
                </button>
              ) : (
                <button
                  onClick={goToNextQuestion}
                  className="inline-flex items-center px-3 sm:px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <span className="hidden xs:inline">Next</span>
                  <ChevronRight className="ml-1 sm:ml-2 h-4 w-4" />
                </button>
              )}
            </div>
            
            <div className="mt-6 overflow-x-auto pb-2">
              <QuestionNavigation
                totalQuestions={questions.length}
                currentIndex={currentQuestionIndex}
                answeredQuestions={answers}
                onNavigate={goToQuestion}
              />
            </div>
            
            {violations.length > 0 && (
              <div className="mt-4 bg-red-50 border-l-4 border-red-400 p-4">
                <div className="flex flex-col sm:flex-row">
                  <div className="flex-shrink-0 mb-2 sm:mb-0 sm:mr-3">
                    <AlertTriangle className="h-5 w-5 text-red-400" />
                  </div>
                  <div>
                    <p className="text-sm text-red-700">
                      Violations detected. Please ensure you remain visible in the camera frame.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
        
        {/* Proctoring sidebar - hidden on small screens, shown at bottom on medium screens */}
        <div className="block lg:hidden w-full border-t border-gray-200 bg-white">
          <details className="w-full">
            <summary className="px-4 py-3 font-medium text-gray-900 cursor-pointer flex items-center justify-between hover:bg-gray-50">
              <div className="flex items-center">
                <Camera className="h-5 w-5 mr-2 text-blue-500" />
                <span>Proctoring Status</span>
              </div>
              <ChevronRight className="h-5 w-5 text-gray-500" />
            </summary>
            <div className="p-4 border-t border-gray-200">
              <ProctoringSidebar
                ref={proctoringSidebarRef}
                violations={violations}
                onViolation={handleViolation}
                isTestEnded={isTestCompleted || testEnded}
              />
            </div>
          </details>
        </div>
        
        {/* Show sidebar normally on large screens */}
        <div className="hidden lg:block">
          <ProctoringSidebar
            ref={proctoringSidebarRef}
            violations={violations}
            onViolation={handleViolation}
            isTestEnded={isTestCompleted || testEnded}
          />
        </div>
      </div>
    </div>
  );
};

export default TestPage;