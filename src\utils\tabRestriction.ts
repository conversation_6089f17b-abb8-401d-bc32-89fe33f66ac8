/**
 * Utility functions to handle tab restriction during tests
 */

type TabChangeCallback = (count: number) => void;

// Store global event handlers so they can be properly removed
let globalHandleVisibilityChange: ((e: Event) => void) | null = null;
// Track if restriction is already enabled to prevent duplicate calls
let restrictionActive = false;

/**
 * Enable tab change detection and restriction
 * @param onTabChange Callback to be called when a tab change is detected
 * @returns Cleanup function to remove event listeners
 */
export const enableTabRestriction = (onTabChange: TabChangeCallback): (() => void) => {
  // If restriction is already active, just return the cleanup function
  if (restrictionActive) {
    console.log('Tab restriction already active, skipping duplicate setup');
    return () => disableTabRestriction();
  }
  
  // First, ensure we clean up any previous restrictions
  disableTabRestriction();
  
  // Mark restriction as active
  restrictionActive = true;
  console.log('Tab restriction now active');
  
  // Start with existing tab change count from localStorage or 0 if none exists
  let tabChangeCount = 0;
  try {
    const storedCount = localStorage.getItem('tabChangeCount');
    if (storedCount) {
      tabChangeCount = parseInt(storedCount, 10);
    }
  } catch (err) {
    console.error('Error retrieving tab change count:', err);
  }
  
  let lastVisibilityState = document.visibilityState;
  let tabChangeDebounce = false;
  
  // Keep track of the original page title
  const originalTitle = document.title;
  
  // Handle visibility change - the core tab change detection
  const handleVisibilityChange = (e: Event) => {
    // Avoid false positives by checking if the visibility state has actually changed
    if (document.visibilityState === lastVisibilityState) {
      return;
    }
    
    // Update the last known state
    lastVisibilityState = document.visibilityState;
    
    if (document.visibilityState === 'hidden') {
      // Debounce tab change detection to avoid duplicates
      if (tabChangeDebounce) {
        return;
      }
      tabChangeDebounce = true;
      setTimeout(() => {
        tabChangeDebounce = false;
      }, 1000);
      
      // Increment tab change count when tab becomes hidden
      tabChangeCount++;
      console.log('Tab change detected, count:', tabChangeCount);
      
      // Store current tab change count in localStorage to persist across refreshes
      try {
        localStorage.setItem('tabChangeCount', tabChangeCount.toString());
      } catch (err) {
        console.error('Error storing tab change count:', err);
      }
      
      // Call the callback with the updated count
      onTabChange(tabChangeCount);
      
      // Change the page title to alert the user
      document.title = '⚠️ RETURN TO TEST IMMEDIATELY! ⚠️';
    } else if (document.visibilityState === 'visible') {
      // Restore the original title
      document.title = originalTitle;
    }
  };
  
  // Store event handler globally for proper cleanup
  globalHandleVisibilityChange = handleVisibilityChange;
  
  // Set up visibility change event listener - the main way to detect tab changes
  document.addEventListener('visibilitychange', handleVisibilityChange);
  
  // Add navigation blocker
  window.onbeforeunload = (e) => {
    e.preventDefault();
    e.returnValue = '⚠️ WARNING: Leaving this page is not allowed during the test and will be recorded as a violation.';
    return e.returnValue;
  };
  
  // Return cleanup function
  return () => {
    // Use the disableTabRestriction function for consistent cleanup
    disableTabRestriction();
  };
};

/**
 * Disable tab change detection and restrictions
 */
export const disableTabRestriction = (): void => {
  // Set the restriction as inactive
  restrictionActive = false;
  
  // We no longer clear tab change count from localStorage to maintain the count across reloads
  // and multiple enable/disable cycles
  
  // Clear document listeners with proper handler references
  if (globalHandleVisibilityChange) {
    document.removeEventListener('visibilitychange', globalHandleVisibilityChange);
  }
  
  // Reset global handlers
  globalHandleVisibilityChange = null;
  
  // Remove navigation blocker
  window.onbeforeunload = null;
}; 