import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  Search, 
  Download,
  Filter,
  ChevronDown,
  ChevronUp,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Calendar,
  Loader,
  Eye,
  X
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { getAllTests, TestResult, getTestDetail, TestDetailResponse } from '../../utils/api';
import toast from 'react-hot-toast';

const ResultManagement = () => {
  const navigate = useNavigate();
  const { token } = useAuth();
  
  const [results, setResults] = useState<TestResult[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<keyof TestResult>('start_time');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [dateFilter, setDateFilter] = useState('all');
  const [selectedTest, setSelectedTest] = useState<TestDetailResponse | null>(null);
  const [loadingTestDetail, setLoadingTestDetail] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  
  useEffect(() => {
    const fetchResults = async () => {
      if (!token) return;
      
      try {
        setLoading(true);
        const response = await getAllTests(token);
        setResults(response.tests || []);
      } catch (error) {
        console.error('Error fetching test results:', error);
        toast.error('Failed to load test results');
      } finally {
        setLoading(false);
      }
    };
    
    fetchResults();
  }, [token]);
  
  const handleSort = (field: keyof TestResult) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };
  
  const handleViewTestDetail = async (testId: number) => {
    if (!token) return;
    
    try {
      setLoadingTestDetail(true);
      const testDetail = await getTestDetail(testId, token);
      setSelectedTest(testDetail);
      setIsModalOpen(true);
    } catch (error) {
      console.error('Error fetching test details:', error);
      toast.error('Failed to load test details');
    } finally {
      setLoadingTestDetail(false);
    }
  };
  
  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedTest(null);
  };
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };
  
  const formatTime = (startTime: string, endTime: string | null) => {
    if (!endTime) return 'In progress';
    
    const start = new Date(startTime).getTime();
    const end = new Date(endTime).getTime();
    const durationInSeconds = Math.floor((end - start) / 1000);
    
    const minutes = Math.floor(durationInSeconds / 60);
    const remainingSeconds = durationInSeconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };
  
  const getScoreClass = (score: number) => {
    if (score >= 80) return 'bg-green-100 text-green-800';
    if (score >= 60) return 'bg-blue-100 text-blue-800';
    if (score >= 40) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };
  
  const exportCSV = () => {
    const headers = [
      'Name',
      'Roll Number',
      'Mobile',
      'Start Time',
      'End Time',
      'Completed',
      'Score'
    ];
    
    const rows = filteredResults.map(result => [
      result.name,
      result.college_roll_number,
      result.mobile_number,
      formatDate(result.start_time),
      result.end_time ? formatDate(result.end_time) : 'In progress',
      result.is_completed === 1 ? 'Yes' : 'No',
      result.score
    ]);
    
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.setAttribute('hidden', '');
    a.setAttribute('href', url);
    a.setAttribute('download', 'test_results.csv');
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };
  
  const filteredResults = results
    .filter(result => {
      // Search filter
      const searchMatch = 
        (result.name?.toLowerCase().includes(searchTerm.toLowerCase()) || false) ||
        (result.college_roll_number?.toLowerCase().includes(searchTerm.toLowerCase()) || false) ||
        (result.mobile_number?.includes(searchTerm) || false);
      
      // Date filter
      let dateMatch = true;
      if (dateFilter !== 'all') {
        const resultDate = new Date(result.start_time);
        const now = new Date();
        
        if (dateFilter === 'today') {
          dateMatch = 
            resultDate.getDate() === now.getDate() &&
            resultDate.getMonth() === now.getMonth() &&
            resultDate.getFullYear() === now.getFullYear();
        } else if (dateFilter === 'week') {
          const weekAgo = new Date();
          weekAgo.setDate(now.getDate() - 7);
          dateMatch = resultDate >= weekAgo;
        } else if (dateFilter === 'month') {
          const monthAgo = new Date();
          monthAgo.setMonth(now.getMonth() - 1);
          dateMatch = resultDate >= monthAgo;
        }
      }
      
      return searchMatch && dateMatch;
    })
    .sort((a, b) => {
      if (sortField === 'start_time' || sortField === 'end_time') {
        const aValue = a[sortField] ? new Date(a[sortField]).getTime() : 0;
        const bValue = b[sortField] ? new Date(b[sortField]).getTime() : 0;
        
        return sortDirection === 'asc'
          ? aValue - bValue
          : bValue - aValue;
      } else if (sortField === 'score' || sortField === 'is_completed') {
        // @ts-ignore - Handle numeric fields
        return sortDirection === 'asc'
          ? a[sortField] - b[sortField]
          : b[sortField] - a[sortField];
      } else {
        // Handle string fields with type casting to ensure they're strings
        const aValue = String(a[sortField] || '');
        const bValue = String(b[sortField] || '');
        
        return sortDirection === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }
    });
  
  // Calculate statistics
  const completedTests = filteredResults.filter(result => result.is_completed === 1);
  
  const stats = {
    totalTests: filteredResults.length,
    completedTests: completedTests.length,
    avgScore: completedTests.length > 0
      ? completedTests.reduce((sum, result) => sum + result.score, 0) / completedTests.length
      : 0,
    highScore: completedTests.length > 0
      ? Math.max(...completedTests.map(result => result.score))
      : 0,
    lowScore: completedTests.length > 0
      ? Math.min(...completedTests.map(result => result.score))
      : 0,
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex flex-col items-center">
          <Loader className="h-8 w-8 text-blue-600 animate-spin" />
          <p className="mt-4 text-gray-600">Loading test results...</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex items-center">
          <button
            onClick={() => navigate('/admin')}
            className="mr-4 text-gray-400 hover:text-gray-500"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <h1 className="text-xl font-semibold text-gray-900">Test Results</h1>
        </div>
      </div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <BarChart className="h-5 w-5 text-blue-500 mr-2" />
              <h3 className="text-sm font-medium text-gray-900">Total Tests</h3>
            </div>
            <p className="mt-2 text-3xl font-semibold text-gray-900">{stats.totalTests}</p>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <PieChart className="h-5 w-5 text-green-500 mr-2" />
              <h3 className="text-sm font-medium text-gray-900">Average Score</h3>
            </div>
            <p className="mt-2 text-3xl font-semibold text-gray-900">{stats.avgScore.toFixed(1)}</p>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <BarChart className="h-5 w-5 text-green-500 mr-2" />
              <h3 className="text-sm font-medium text-gray-900">Highest Score</h3>
            </div>
            <p className="mt-2 text-3xl font-semibold text-gray-900">{stats.highScore}</p>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <BarChart className="h-5 w-5 text-red-500 mr-2" />
              <h3 className="text-sm font-medium text-gray-900">Lowest Score</h3>
            </div>
            <p className="mt-2 text-3xl font-semibold text-gray-900">{stats.lowScore}</p>
          </div>
        </div>
        
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full sm:w-auto">
              <div className="relative rounded-md w-full sm:w-64">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="Search by name or roll number..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <div className="relative rounded-md w-full sm:w-auto">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Calendar className="h-5 w-5 text-gray-400" />
                </div>
                <select
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                >
                  <option value="all">All Time</option>
                  <option value="today">Today</option>
                  <option value="week">Last 7 Days</option>
                  <option value="month">Last 30 Days</option>
                </select>
              </div>
            </div>
            
            <button
              onClick={exportCSV}
              className="flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Download className="mr-2 h-4 w-4" />
              Export Results
            </button>
          </div>
          
          <div className="overflow-x-auto">
            {filteredResults.length > 0 ? (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('name')}
                    >
                      <div className="flex items-center">
                        Student
                        {sortField === 'name' && (
                          sortDirection === 'asc' ? <ChevronUp className="ml-1 h-4 w-4" /> : <ChevronDown className="ml-1 h-4 w-4" />
                        )}
                      </div>
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('college_roll_number')}
                    >
                      <div className="flex items-center">
                        Roll Number
                        {sortField === 'college_roll_number' && (
                          sortDirection === 'asc' ? <ChevronUp className="ml-1 h-4 w-4" /> : <ChevronDown className="ml-1 h-4 w-4" />
                        )}
                      </div>
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('start_time')}
                    >
                      <div className="flex items-center">
                        Test Date
                        {sortField === 'start_time' && (
                          sortDirection === 'asc' ? <ChevronUp className="ml-1 h-4 w-4" /> : <ChevronDown className="ml-1 h-4 w-4" />
                        )}
                      </div>
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('is_completed')}
                    >
                      <div className="flex items-center">
                        Status
                        {sortField === 'is_completed' && (
                          sortDirection === 'asc' ? <ChevronUp className="ml-1 h-4 w-4" /> : <ChevronDown className="ml-1 h-4 w-4" />
                        )}
                      </div>
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('score')}
                    >
                      <div className="flex items-center">
                        Score
                        {sortField === 'score' && (
                          sortDirection === 'asc' ? <ChevronUp className="ml-1 h-4 w-4" /> : <ChevronDown className="ml-1 h-4 w-4" />
                        )}
                      </div>
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Duration
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Action
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredResults.map((result, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{result.name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{result.college_roll_number}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{formatDate(result.start_time)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            result.is_completed === 1
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}
                        >
                          {result.is_completed === 1 ? 'Completed' : 'In Progress'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {result.is_completed === 1 ? (
                          <div className={`text-sm font-medium ${getScoreClass(result.score)}`}>
                            {result.score}
                          </div>
                        ) : (
                          <div className="text-sm text-gray-500">-</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatTime(result.start_time, result.end_time)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <button
                          onClick={() => handleViewTestDetail(result.id)}
                          className="text-blue-600 hover:text-blue-800 transition-colors flex items-center"
                          disabled={loadingTestDetail}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            ) : (
              <div className="text-center py-10">
                <p className="text-gray-500">No results found</p>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Test Detail Modal */}
      {isModalOpen && selectedTest && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-auto">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center sticky top-0 bg-white">
              <h2 className="text-lg font-medium text-gray-900">
                Test Result Details
              </h2>
              <button
                onClick={closeModal}
                className="text-gray-400 hover:text-gray-500"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            
            <div className="p-6">
              {/* Student Information */}
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-3">
                  Student Information
                </h3>
                <div className="bg-gray-50 rounded-lg p-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-xs text-gray-500">Name</p>
                    <p className="text-sm font-medium">{selectedTest.test.studentName}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Mobile Number</p>
                    <p className="text-sm font-medium">{selectedTest.test.mobileNumber}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">College Email</p>
                    <p className="text-sm font-medium">{selectedTest.test.email}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">College Roll Number</p>
                    <p className="text-sm font-medium">{selectedTest.test.collegeRollNumber}</p>
                  </div>
                </div>
              </div>
              
              {/* Test Summary */}
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-3">
                  Test Summary
                </h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <p className="text-xs text-gray-500">Started</p>
                      <p className="text-sm font-medium">
                        {new Date(selectedTest.test.startTime).toLocaleString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Completed</p>
                      <p className="text-sm font-medium">
                        {new Date(selectedTest.test.endTime).toLocaleString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Duration</p>
                      <p className="text-sm font-medium">{selectedTest.test.duration} minutes</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Score</p>
                      <p className="text-sm font-semibold text-blue-600">{selectedTest.test.score}%</p>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Test Statistics */}
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-3">
                  Test Statistics
                </h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <div>
                      <p className="text-xs text-gray-500">Total Questions</p>
                      <p className="text-sm font-medium">{selectedTest.test.stats.totalQuestions}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Attempted</p>
                      <p className="text-sm font-medium">{selectedTest.test.stats.attemptedQuestions}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Not Attempted</p>
                      <p className="text-sm font-medium">{selectedTest.test.stats.notAttemptedQuestions}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Correct Answers</p>
                      <p className="text-sm font-medium text-green-600">{selectedTest.test.stats.correctAnswers}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Wrong Answers</p>
                      <p className="text-sm font-medium text-red-600">{selectedTest.test.stats.wrongAnswers}</p>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Question Type Analysis */}
              <div>
                <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-3">
                  Question Type Analysis
                </h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="border border-gray-200 rounded p-3">
                      <h4 className="text-sm font-medium mb-2">Logical Reasoning</h4>
                      <div className="flex justify-between">
                        <div>
                          <p className="text-xs text-gray-500">Total</p>
                          <p className="text-sm font-medium">{selectedTest.test.stats.logical_reasoning_total}</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500">Correct</p>
                          <p className="text-sm font-medium">{selectedTest.test.stats.logical_reasoning_correct}</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500">Accuracy</p>
                          <p className="text-sm font-medium">
                            {Math.round((parseInt(selectedTest.test.stats.logical_reasoning_correct) / selectedTest.test.stats.logical_reasoning_total) * 100)}%
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="border border-gray-200 rounded p-3">
                      <h4 className="text-sm font-medium mb-2">Common Sense</h4>
                      <div className="flex justify-between">
                        <div>
                          <p className="text-xs text-gray-500">Total</p>
                          <p className="text-sm font-medium">{selectedTest.test.stats.common_sense_total}</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500">Correct</p>
                          <p className="text-sm font-medium">{selectedTest.test.stats.common_sense_correct}</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500">Accuracy</p>
                          <p className="text-sm font-medium">
                            {Math.round((parseInt(selectedTest.test.stats.common_sense_correct) / selectedTest.test.stats.common_sense_total) * 100)}%
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ResultManagement;